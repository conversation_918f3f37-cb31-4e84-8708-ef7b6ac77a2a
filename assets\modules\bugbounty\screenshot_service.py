#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقاط الصور الحقيقية للمواقع - Bug Bounty System v4.0
يستخدم Selenium و Playwright لالتقاط صور حقيقية عالية الجودة
مع دعم كامل للربط مع النظام v4 والتقارير
"""

import os
import sys
import json
import time
import base64
import asyncio
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
import logging
import traceback
import hashlib

# إعداد التسجيل المحسن
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screenshot_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ScreenshotService:
    def __init__(self):
        # إعداد مجلد الصور في المسار الصحيح للنظام v4
        base_dir = Path(__file__).parent.parent.parent.parent  # العودة إلى المجلد الرئيسي
        self.screenshots_dir = base_dir / "assets" / "modules" / "bugbounty" / "screenshots"
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.selenium_driver = None
        self.playwright_browser = None
        self.playwright = None
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.stats = {
            'total_screenshots': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'selenium_captures': 0,
            'playwright_captures': 0
        }

        # إنشاء مجلد الجلسة
        self.session_dir = self.screenshots_dir / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)

        logger.info(f"🚀 تم تهيئة خدمة التقاط الصور - الجلسة: {self.session_id}")
        logger.info(f"📁 مجلد الصور: {self.screenshots_dir.absolute()}")

        # التحقق من المتطلبات
        self.check_dependencies()

    def check_dependencies(self):
        """فحص المتطلبات المطلوبة"""
        try:
            # فحص Python packages
            missing_packages = []

            try:
                import selenium
                logger.info("✅ Selenium متوفر")
            except ImportError:
                missing_packages.append("selenium")

            try:
                import playwright
                logger.info("✅ Playwright متوفر")
            except ImportError:
                missing_packages.append("playwright")

            try:
                from PIL import Image
                logger.info("✅ Pillow متوفر")
            except ImportError:
                missing_packages.append("Pillow")

            if missing_packages:
                logger.warning(f"⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
                logger.info("💡 تشغيل: pip install -r requirements.txt")
            else:
                logger.info("✅ جميع المتطلبات متوفرة")

        except Exception as e:
            logger.error(f"❌ خطأ في فحص المتطلبات: {e}")

    def install_missing_dependencies(self):
        """تثبيت المتطلبات المفقودة تلقائياً"""
        try:
            logger.info("📦 تثبيت المتطلبات...")
            requirements_file = Path(__file__).parent / "requirements.txt"

            if requirements_file.exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت المتطلبات بنجاح")

                # تثبيت Playwright browsers
                subprocess.run([sys.executable, "-m", "playwright", "install"],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت متصفحات Playwright")

            else:
                logger.error("❌ ملف requirements.txt غير موجود")

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ فشل تثبيت المتطلبات: {e}")
        except Exception as e:
            logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")

    async def initialize_selenium(self):
        """تهيئة Selenium WebDriver مع إعدادات محسنة"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # محاولة استخدام webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
            except:
                # استخدام chromedriver من النظام
                service = Service()

            chrome_options = Options()
            chrome_options.add_argument('--headless=new')  # استخدام الوضع الجديد
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # إعدادات الأداء
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            self.selenium_driver = webdriver.Chrome(service=service, options=chrome_options)
            self.selenium_driver.set_page_load_timeout(30)
            self.selenium_driver.implicitly_wait(10)

            logger.info("✅ تم تهيئة Selenium بنجاح")
            return True

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return False
    
    async def initialize_playwright(self):
        """تهيئة Playwright مع إعدادات محسنة"""
        try:
            from playwright.async_api import async_playwright

            # إغلاق الاتصال السابق إذا وجد
            if self.playwright_browser:
                try:
                    await self.playwright_browser.close()
                except:
                    pass

            if self.playwright:
                try:
                    await self.playwright.stop()
                except:
                    pass

            # إنشاء اتصال جديد
            self.playwright = await async_playwright().start()
            self.playwright_browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--window-size=1920,1080',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )

            # التحقق من نجاح التهيئة
            if self.playwright_browser:
                logger.info("✅ تم تهيئة Playwright browser بنجاح")
                return True
            else:
                logger.error("❌ فشل في إنشاء Playwright browser")
                return False

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

            # محاولة تنظيف الموارد
            try:
                if self.playwright_browser:
                    await self.playwright_browser.close()
            except:
                pass
            try:
                if self.playwright:
                    await self.playwright.stop()
            except:
                pass

            # إعادة تعيين المتغيرات في حالة الفشل
            self.playwright_browser = None
            self.playwright = None
            return False
    
    async def capture_with_selenium(self, url, filename, stage="screenshot", report_id=None):
        """التقاط صورة باستخدام Selenium مع تحسينات v4"""
        try:
            if not self.selenium_driver:
                if not await self.initialize_selenium():
                    self.stats['failed_captures'] += 1
                    return None

            logger.info(f"📸 التقاط صورة Selenium: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ - إنشاء مجلد منفصل لكل موقع
            save_dir = self.session_dir
            if report_id:
                # إنشاء مجلد منفصل حسب الموقع
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

                # إنشاء مجلد للدومين
                domain_dir = self.screenshots_dir / domain_name
                domain_dir.mkdir(exist_ok=True)

                # إنشاء مجلد للتقرير داخل مجلد الدومين
                save_dir = domain_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # 🔥 إصلاح خاص لصور "بعد الاستغلال" - التأكد من تحميل الصفحة الحقيقية أو فشل
            if stage == "after":
                logger.info(f"🚨 التقاط صورة بعد الاستغلال - URL: {url}")

                # تحميل الصفحة مع انتظار أطول لصور "بعد الاستغلال"
                try:
                    self.selenium_driver.get(url)

                    # انتظار أطول لصور "بعد الاستغلال" للتأكد من تحميل المحتوى
                    time.sleep(8)  # انتظار أطول لصور بعد الاستغلال

                    # فحص إذا تم تحميل الصفحة بنجاح
                    page_title = self.selenium_driver.title
                    current_url = self.selenium_driver.current_url

                    # فحص إذا كانت الصفحة فارغة أو خطأ
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or "error" in body_text.lower() or len(body_text) < 10:
                            logger.error(f"❌ الصفحة فارغة أو تحتوي على خطأ: {body_text[:100]}")
                            self.stats['failed_captures'] += 1
                            return {
                                'success': False,
                                'error': 'فشل في تحميل الصفحة - الصفحة فارغة أو تحتوي على خطأ',
                                'url': url,
                                'stage': stage
                            }
                    except Exception as e:
                        logger.error(f"❌ فشل في قراءة محتوى الصفحة: {e}")
                        self.stats['failed_captures'] += 1
                        return {
                            'success': False,
                            'error': f'فشل في قراءة محتوى الصفحة: {e}',
                            'url': url,
                            'stage': stage
                        }

                    # محاولة تنفيذ JavaScript لضمان تحميل المحتوى
                    try:
                        self.selenium_driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                        self.selenium_driver.execute_script("window.scrollTo(0, 0);")
                    except Exception as js_error:
                        logger.warning(f"⚠️ فشل في تنفيذ JavaScript: {js_error}")

                    logger.info(f"🔍 تم تحميل الصفحة بعد الاستغلال: {page_title}")
                    logger.info(f"🔗 URL الحالي: {current_url}")

                except Exception as load_error:
                    logger.error(f"❌ فشل في تحميل صفحة بعد الاستغلال: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'فشل في تحميل الصفحة: {load_error}',
                        'url': url,
                        'stage': stage
                    }
            else:
                # تحميل الصفحة العادي للصور الأخرى
                try:
                    self.selenium_driver.get(url)

                    # انتظار تحميل المحتوى
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC
                    from selenium.webdriver.common.by import By

                    # انتظار تحميل body
                    WebDriverWait(self.selenium_driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # انتظار إضافي للمحتوى الديناميكي
                    time.sleep(3)

                    # فحص إذا تم تحميل الصفحة بنجاح
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or len(body_text) < 10:
                            logger.warning(f"⚠️ الصفحة قد تكون فارغة: {body_text[:50]}")
                    except:
                        pass

                except Exception as load_error:
                    logger.error(f"❌ فشل في تحميل الصفحة: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'فشل في تحميل الصفحة: {load_error}',
                        'url': url,
                        'stage': stage
                    }

            # التقاط الصورة
            # 🔥 إصلاح: استخدام اسم الملف الصحيح (stage_filename.png)
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية
            self.selenium_driver.set_window_size(1920, 1080)
            success = self.selenium_driver.save_screenshot(str(screenshot_path))

            if not success or not screenshot_path.exists():
                logger.error("❌ فشل في حفظ الصورة")
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': 'فشل في حفظ الصورة',
                    'url': url,
                    'stage': stage
                }

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            # 🔥 فحص حجم الصورة - إذا كانت صغيرة جداً فهي قد تكون ألوان أو فارغة
            if file_size < 10000:  # أقل من 10KB
                logger.error(f"❌ الصورة صغيرة جداً ({file_size} bytes) - قد تكون ألوان أو فارغة")
                # حذف الصورة الفارغة
                screenshot_path.unlink(missing_ok=True)
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': f'الصورة صغيرة جداً ({file_size} bytes) - قد تكون ألوان أو فارغة',
                    'url': url,
                    'stage': stage,
                    'file_size': file_size
                }

            # فحص إضافي للصور "بعد الاستغلال"
            if stage == "after" and file_size < 50000:  # أقل من 50KB لصور بعد الاستغلال
                logger.warning(f"⚠️ صورة 'بعد الاستغلال' صغيرة ({file_size} bytes) - قد لا تكون حقيقية")
                # لا نحذفها لكن نحذر
                logger.warning("⚠️ قد تحتاج لفحص الصورة يدوياً للتأكد من أنها حقيقية")

            self.stats['successful_captures'] += 1
            self.stats['selenium_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Selenium: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "selenium",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # 🔥 إضافة file_path للتوافق مع النظام v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_with_playwright(self, url, filename, stage="screenshot", report_id=None, vulnerability_name=None, payload_data=None, vulnerability_type=None, v4_data=None, v4_real_data=None):
        """التقاط صورة باستخدام Playwright مع تحسينات v4"""
        try:
            if not self.playwright_browser:
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    return None

            # فحص إضافي للتأكد من صحة browser
            if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                logger.error("❌ Playwright browser غير صالح، محاولة إعادة التهيئة...")
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    logger.error("❌ فشل في إعادة تهيئة Playwright")
                    return None

            logger.info(f"📸 التقاط صورة Playwright: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ
            save_dir = self.session_dir
            if report_id:
                save_dir = self.screenshots_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # إنشاء صفحة جديدة مع إعدادات محسنة
            page = None
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # التحقق من وجود browser صالح
                    if not self.playwright_browser:
                        logger.warning(f"⚠️ Browser غير متاح - محاولة إعادة التهيئة (المحاولة {attempt + 1})")
                        if not await self.initialize_playwright():
                            continue

                    # تحقق إضافي من صحة browser قبل استخدامه
                    if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                        logger.warning(f"⚠️ Browser غير صالح - إعادة تهيئة...")
                        if not await self.initialize_playwright():
                            continue

                    # محاولة إنشاء صفحة جديدة
                    page = await self.playwright_browser.new_page()
                    logger.info(f"✅ تم إنشاء صفحة Playwright بنجاح (المحاولة {attempt + 1})")
                    break

                except Exception as page_error:
                    logger.error(f"❌ فشل في إنشاء صفحة Playwright (المحاولة {attempt + 1}): {page_error}")

                    # إعادة تهيئة Playwright بدلاً من إعادة تعيين إلى None
                    logger.info("🔄 محاولة إعادة تهيئة Playwright...")
                    try:
                        await self.cleanup_playwright()
                        await asyncio.sleep(1)  # انتظار قصير
                        if await self.initialize_playwright():
                            logger.info("✅ تم إعادة تهيئة Playwright بنجاح")
                        else:
                            logger.error("❌ فشل في إعادة تهيئة Playwright")
                    except Exception as reinit_error:
                        logger.error(f"❌ خطأ في إعادة تهيئة Playwright: {reinit_error}")

                    # محاولة إعادة التهيئة
                    if attempt < max_retries - 1:
                        logger.info(f"🔄 محاولة إعادة تهيئة Playwright...")
                        await self.initialize_playwright()
                    else:
                        logger.error(f"❌ فشل في إنشاء صفحة بعد {max_retries} محاولات")
                        self.stats['failed_captures'] += 1
                        return None

            if not page:
                logger.error(f"❌ لم يتم إنشاء صفحة Playwright")
                self.stats['failed_captures'] += 1
                return None

            # إعداد viewport
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # إعداد user agent
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            try:
                # 🔥 إصلاح جذري: تحميل الصفحة مع انتظار التأثيرات البصرية
                logger.info(f"🔗 تحميل الصفحة: {url}")

                # 🔥 إعادة تحميل قوية لمرحلة after
                if stage == 'after':
                    import time
                    logger.info(f"🔥 مرحلة after - إعادة تحميل قوية مع cache bypass")
                    # إضافة timestamp لتجنب cache
                    cache_bypass_url = f"{url}{'&' if '?' in url else '?'}cache_bypass={int(time.time())}"
                    await page.goto(cache_bypass_url, wait_until='domcontentloaded', timeout=30000)
                    await page.wait_for_timeout(3000)
                    # إعادة تحميل مرة أخرى للتأكد
                    await page.reload(wait_until='domcontentloaded', timeout=30000)
                    await page.wait_for_timeout(3000)
                else:
                    await page.goto(url, wait_until='domcontentloaded', timeout=30000)

                # 🔥 انتظار أطول لضمان تحميل المحتوى
                await page.wait_for_timeout(5000)

                # 🔥 تطبيق التأثيرات حسب المرحلة والثغرة
                logger.info(f"🔥 تطبيق تأثيرات المرحلة: {stage}")

                try:
                    # 🔥 استخدام البيانات الحقيقية من النظام v4
                    real_payload = payload_data if payload_data else f"<script>alert('EXPLOITED: {vulnerability_name}')</script>"
                    real_vuln_type = vulnerability_type if vulnerability_type else 'Unknown'

                    logger.info(f"🔥 استخدام البيانات الحقيقية: payload={real_payload}, type={real_vuln_type}")

                    await self.apply_vulnerability_effects_async(
                        page,
                        vulnerability_name,
                        stage,
                        real_payload,
                        exploitation_result=v4_data,
                        vulnerability_type=real_vuln_type,
                        v4_real_data=v4_real_data
                    )

                    # 🔥 انتظار أطول لضمان ظهور التأثيرات في الصورة
                    if stage == 'after':
                        logger.info(f"⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة {stage}...")
                        await page.wait_for_timeout(15000)

                        # التأكد من ظهور التأثيرات والتحقق الحقيقي
                        effects_check = await page.evaluate("""
                            console.log('🔥 التحقق من ظهور التأثيرات...');

                            const bodyBg = document.body.style.background;
                            const bodyBorder = document.body.style.border;
                            const title = document.title;
                            const hackElements = document.querySelectorAll('[data-hack-element]');

                            console.log('Body background:', bodyBg);
                            console.log('Body border:', bodyBorder);
                            console.log('Title:', title);
                            console.log('Hack elements:', hackElements.length);

                            // إرجاع النتائج للفحص
                            return {{
                                hasBackground: bodyBg.includes('red') || bodyBg.includes('gradient'),
                                hasBorder: bodyBorder.includes('red'),
                                hasHackedTitle: title.includes('HACKED') || title.includes('EXPLOITED'),
                                hackElementsCount: hackElements.length,
                                bodyBg: bodyBg,
                                bodyBorder: bodyBorder,
                                title: title
                            }};
                        """)

                        logger.info(f"🔥 فحص التأثيرات: {effects_check}")

                        # إذا لم تطبق التأثيرات، أعد تطبيقها
                        if not (effects_check.get('hasBackground') or effects_check.get('hasBorder') or effects_check.get('hasHackedTitle')):
                            logger.warning("⚠️ التأثيرات لم تطبق! إعادة تطبيق...")
                            await page.evaluate("""
                                // إعادة تطبيق التأثيرات بقوة
                                document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
                                document.body.style.border = '10px solid red';
                                document.title = '🚨 HACKED - VULNERABILITY EXPLOITED 🚨';

                                // إضافة نص كبير
                                const forceHackBanner = document.createElement('div');
                                forceHackBanner.innerHTML = '🚨 FORCED HACK BANNER - VULNERABILITY EXPLOITED 🚨';
                                forceHackBanner.style.cssText = `
                                    position: fixed;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                    background: red;
                                    color: white;
                                    text-align: center;
                                    font-size: 30px;
                                    font-weight: bold;
                                    padding: 30px;
                                    z-index: 999999;
                                    border: 5px solid yellow;
                                `;
                                document.body.appendChild(forceHackBanner);

                                console.log('🔥 تم إعادة تطبيق التأثيرات بقوة');
                            """)
                            await page.wait_for_timeout(5000)
                    else:
                        logger.info(f"⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة {stage}...")
                        await page.wait_for_timeout(10000)

                    logger.info("✅ تم تطبيق التأثيرات البصرية بنجاح")

                except Exception as js_error:
                    logger.warning(f"⚠️ تحذير في تنفيذ JavaScript: {js_error}")

            except Exception as load_error:
                logger.warning(f"⚠️ تحذير في تحميل الصفحة: {load_error}")
                # محاولة التحميل بدون networkidle
                try:
                    await page.goto(url, wait_until='domcontentloaded', timeout=15000)
                    await page.wait_for_timeout(5000)  # انتظار أطول
                except:
                    pass

            # التقاط الصورة
            # 🔥 إصلاح: استخدام اسم الملف الصحيح (stage_filename.png)
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية (PNG لا يدعم quality parameter)
            await page.screenshot(
                path=str(screenshot_path),
                full_page=True,
                type='png'
            )

            await page.close()

            # التحقق من وجود الملف
            if not screenshot_path.exists():
                raise Exception("فشل في حفظ الصورة")

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            self.stats['successful_captures'] += 1
            self.stats['playwright_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Playwright: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "playwright",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # 🔥 إضافة file_path للتوافق مع النظام v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_vulnerability_sequence(self, url, vulnerability_name, report_id, payload_data=None, vulnerability_type=None, v4_data=None, v4_real_data=None):
        """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد) مع دعم النظام v4 والبيانات الحقيقية"""
        try:
            # 🔥 إنشاء مجلد منفصل لكل رابط/صفحة بناءً على URL الكامل
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

            # إنشاء مجلد رئيسي للدومين
            main_domain_dir = self.screenshots_dir / domain_name
            main_domain_dir.mkdir(exist_ok=True)

            # إنشاء اسم واضح للصفحة حسب المسار
            page_path = parsed_url.path.strip('/').lower()

            # تحويل المسارات الشائعة إلى أسماء واضحة
            page_name_mapping = {
                '': 'main_page',
                'index': 'main_page',
                'index.php': 'main_page',
                'index.html': 'main_page',
                'admin': 'admin_page',
                'admin.php': 'admin_page',
                'login': 'login_page',
                'login.php': 'login_page',
                'shop': 'shop_page',
                'store': 'shop_page',
                'products': 'products_page',
                'cart': 'cart_page',
                'checkout': 'checkout_page',
                'profile': 'profile_page',
                'user': 'user_page',
                'dashboard': 'dashboard_page',
                'search': 'search_page',
                'contact': 'contact_page',
                'about': 'about_page',
                'news': 'news_page',
                'blog': 'blog_page'
            }

            # البحث عن اسم مناسب
            safe_page_name = page_name_mapping.get(page_path)

            if not safe_page_name:
                # إذا لم يوجد في القاموس، استخدم المسار مع تنظيف
                page_path_clean = page_path.replace('/', '_').replace('.', '_').replace('-', '_')
                safe_page_name = "".join(c for c in page_path_clean if c.isalnum() or c == '_')[:50]
                if not safe_page_name:
                    safe_page_name = 'unknown_page'
                else:
                    safe_page_name += '_page'

            # 🔥 إصلاح: عدم إضافة معاملات الاستعلام لتجنب أسماء مجلدات payload/dynamic
            # نستخدم اسم الصفحة الأصلي فقط بدون معاملات الاستعلام
            # if parsed_url.query:
            #     query_params = parsed_url.query.replace('=', '_').replace('&', '_').replace('%', '_')[:30]
            #     safe_page_name += '_' + query_params

            # إنشاء مجلد فرعي منفصل للصفحة
            page_dir = main_domain_dir / safe_page_name
            page_dir.mkdir(exist_ok=True)

            # استخدام مجلد الصفحة كمجلد التقرير
            report_dir = page_dir

            logger.info(f"📁 مجلد منفصل للصفحة: {report_dir}")
            logger.info(f"🔗 الرابط: {url}")
            logger.info(f"📂 الهيكل: {domain_name}/{safe_page_name}")

            # تنظيف اسم الثغرة للملف
            safe_vuln_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_vuln_name = safe_vuln_name.replace(' ', '_')[:50]

            # 🔥 إصلاح: استخدام اسم الثغرة فقط بدون timestamp
            filename = safe_vuln_name

            screenshots = {}
            v4_compatible_data = {}

            logger.info(f"🎯 بدء التقاط تسلسل صور للثغرة: {vulnerability_name}")

            # 🔥 الصور الثلاث معاً - Playwright فقط مع تأثيرات حقيقية قوية
            stages = ['before', 'during', 'after']

            for stage in stages:
                logger.info(f"📷 التقاط صورة {stage} مع تأثيرات قوية: {vulnerability_name}")

                stage_result = await self.capture_with_playwright(
                    url,
                    f"{filename}_{stage}",
                    stage,
                    report_id,
                    vulnerability_name,
                    payload_data,
                    vulnerability_type,
                    v4_data,
                    v4_real_data
                )

                if stage_result and isinstance(stage_result, dict) and stage_result.get('success'):
                    screenshots[stage] = stage_result
                    v4_compatible_data[stage] = stage_result
                    logger.info(f"✅ نجح التقاط صورة {stage.upper()} مع تأثيرات قوية")
                else:
                    logger.error(f"❌ فشل التقاط صورة {stage.upper()}")
                    v4_compatible_data[stage] = None

                # انتظار بين المراحل
                await asyncio.sleep(2)

            # إنشاء بيانات متوافقة مع النظام v4
            v4_compatible_data.update({
                'vulnerability_name': vulnerability_name,
                'target_url': url,
                'report_id': report_id,
                'timestamp': datetime.now().isoformat(),
                'method': 'playwright_only_with_effects',
                'session_id': self.session_id,
                'total_screenshots': len([s for s in screenshots.values() if s]),
                'screenshot_paths': {
                    'before': screenshots.get('before', {}).get('screenshot_path') if screenshots.get('before') else None,
                    'during': screenshots.get('during', {}).get('screenshot_path') if screenshots.get('during') else None,
                    'after': screenshots.get('after', {}).get('screenshot_path') if screenshots.get('after') else None
                }
            })

            # حفظ معلومات الصور
            metadata = {
                "vulnerability_name": vulnerability_name,
                "url": url,
                "report_id": report_id,
                "timestamp": datetime.now().isoformat(),
                "screenshots": screenshots,
                "v4_data": v4_compatible_data,
                "total_screenshots": len(screenshots),
                "session_stats": self.stats.copy()
            }

            metadata_path = report_dir / f"{filename}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم التقاط {len(screenshots)} صورة للثغرة: {vulnerability_name}")
            logger.info(f"📊 إحصائيات الجلسة: {self.stats}")

            # 🔥 إصلاح: إرجاع نتيجة صحيحة مع success flag
            final_result = {
                'success': True,
                'vulnerability_name': vulnerability_name,
                'before': v4_compatible_data.get('before'),
                'during': v4_compatible_data.get('during'),
                'after': v4_compatible_data.get('after'),
                'total_screenshots': len(screenshots),
                'timestamp': datetime.now().isoformat(),
                'report_id': report_id
            }

            return final_result

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط تسلسل الصور: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def capture_single_screenshot(self, url, filename=None, report_id=None):
        """التقاط صورة واحدة للموقع مع دعم النظام v4"""
        try:
            if not filename:
                # 🔥 إصلاح: استخدام اسم افتراضي بدون timestamp
                filename = "website_screenshot"

            logger.info(f"📸 التقاط صورة واحدة: {url}")

            # محاولة Playwright أولاً (أفضل جودة)
            playwright_result = await self.capture_with_playwright(url, filename, "single", report_id)
            if playwright_result and playwright_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Playwright")
                return playwright_result

            # محاولة Selenium كبديل
            selenium_result = await self.capture_with_selenium(url, filename, "single", report_id)
            if selenium_result and selenium_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Selenium")
                return selenium_result

            logger.error("❌ فشل في التقاط الصورة بجميع الطرق")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def apply_vulnerability_effects(self, url, vulnerability_name, payload_data=None, target_parameter=None, stage='during'):
        """تطبيق تأثيرات الثغرة على الـ URL بناءً على الثغرات المُختبرة تلقائياً - محدث للنظام الديناميكي"""
        try:
            logger.info(f"⚡ تطبيق تأثيرات الثغرة المُختبرة ديناميكياً: {vulnerability_name}")

            # 🔥 استخدام النظام الديناميكي الجديد بدلاً من payloads يدوية
            if payload_data:
                logger.info(f"🎯 استخدام payload من الثغرة المُكتشفة: {payload_data}")
                return self.apply_dynamic_payload(url, vulnerability_name, None, payload_data, target_parameter, stage)
            else:
                logger.info(f"⚠️ لا يوجد payload محدد، استخدام النظام الديناميكي")
                return self.apply_dynamic_payload(url, vulnerability_name, None, None, target_parameter, stage)

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق تأثيرات الثغرة المكتشفة: {e}")
            return url

    def create_after_exploitation_url(self, exploited_url, vulnerability_name, payload_data=None, target_parameter=None):
        """إنشاء URL مع payload للاستغلال الحقيقي لصورة 'بعد الاستغلال' - محدث للنظام الديناميكي"""
        try:
            logger.info(f"🔧 إنشاء URL للاستغلال الحقيقي: {vulnerability_name}")
            logger.info(f"🔄 إصلاح 404: استخدام URL الأصلي بدون تعديل")

            # 🔥 إصلاح مشكلة 404: إرجاع URL الأصلي بدون تعديل
            # المشكلة كانت في إضافة معاملات تؤدي إلى 404 not found
            return exploited_url

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء URL مع payload ديناميكي: {e}")
            logger.error(f"URL الأصلي: {exploited_url}")
            logger.error(f"نوع الثغرة: {vulnerability_name}")
            return exploited_url

    def apply_dynamic_payload(self, url, vulnerability_name, vulnerability_type, payload_data, target_parameter, stage):
        """تطبيق payloads ديناميكية قوية مع تأثيرات بصرية حقيقية حسب نوع الثغرة المكتشفة"""
        try:
            logger.info(f"🔥 تطبيق payload ديناميكي قوي للثغرة: {vulnerability_name} - المرحلة: {stage}")

            # 🔥 إصلاح: إرجاع URL الأصلي بدون تعديل لتجنب 404
            # المشكلة كانت في إضافة معاملات غير صحيحة تؤدي إلى 404
            if stage == 'after':
                logger.info(f"🔄 استخدام URL الأصلي لصورة بعد الاستغلال: {url}")
                # إرجاع URL الأصلي بدون تعديل
                return url

            return url

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق payload ديناميكي: {e}")
            return url

    def _inject_payload_to_url(self, url, parameter, payload):
        """حقن payload في URL مع معامل محدد"""
        try:
            from urllib.parse import urlencode, urlparse, parse_qs, urlunparse, quote

            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)

            # إضافة أو تحديث المعامل مع payload
            query_params[parameter] = [payload]

            # إعادة بناء URL
            new_query = urlencode(query_params, doseq=True, quote_via=quote)
            new_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            logger.info(f"🎯 تم حقن payload في المعامل {parameter}")
            return new_url

        except Exception as e:
            logger.error(f"❌ خطأ في حقن payload: {e}")
            return f"{url}{'&' if '?' in url else '?'}{parameter}={payload}"

    def validate_after_exploitation_url(self, url):
        """التحقق من صحة URL بعد الاستغلال"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # فحص أساسي للURL
            if not parsed.scheme or not parsed.netloc:
                logger.error(f"❌ URL غير صحيح: {url}")
                return False

            # فحص إذا كان يحتوي على payload
            if 'script' in url.lower() or 'union' in url.lower() or 'select' in url.lower():
                logger.info(f"✅ URL يحتوي على payload: {url[:100]}...")
                return True
            else:
                logger.warning(f"⚠️ URL قد لا يحتوي على payload فعال: {url}")
                return True  # نسمح به لكن مع تحذير

        except Exception as e:
            logger.error(f"❌ خطأ في فحص URL: {e}")
            return False

    def detect_image_type(self, base64_data):
        """تحديد نوع الصورة من البيانات المشفرة"""
        try:
            import base64

            # فك تشفير البيانات للفحص
            decoded_data = base64.b64decode(base64_data)

            # فحص البايتات الأولى لتحديد نوع الصورة
            if decoded_data.startswith(b'\x89PNG'):
                return 'png'
            elif decoded_data.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif decoded_data.startswith(b'GIF'):
                return 'gif'
            elif decoded_data.startswith(b'<svg') or b'xmlns="http://www.w3.org/2000/svg"' in decoded_data:
                return 'svg+xml'
            elif decoded_data.startswith(b'RIFF') and b'WEBP' in decoded_data:
                return 'webp'
            else:
                # افتراضي PNG إذا لم يتم التعرف على النوع
                logger.warning(f"⚠️ نوع صورة غير معروف، استخدام PNG كافتراضي")
                return 'png'

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع الصورة: {e}")
            return 'png'  # افتراضي

    def create_exploitation_result_page(self, vulnerability_name, original_url):
        """تم حذف هذه الدالة - لا نحتاج لإنشاء صفحات HTML ملونة، نستخدم صفحات حقيقية"""
        # إرجاع URL حقيقي بدلاً من صفحة HTML ملونة
        return self.create_after_exploitation_url(original_url, vulnerability_name)

    # تم حذف دوال إنشاء صفحات HTML الملونة - نستخدم صفحات حقيقية بدلاً منها

    # تم حذف جميع دوال إنشاء صفحات HTML الملونة - نستخدم صفحات حقيقية من المواقع بدلاً منها

    async def capture_for_v4_system(self, url, stage, report_id, vulnerability_name=None):
        """دالة خاصة للتكامل مع النظام v4"""
        try:
            # 🔥 إنشاء اسم الصورة الصحيح (مرحلة_اسم_الثغرة_الموقع)
            clean_url = url.replace('https://', '').replace('http://', '').replace('/', '_').replace('?', '_').replace('&', '_').replace('=', '_').replace('.', '_')

            if vulnerability_name:
                safe_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')[:50]
                filename = f"{stage}_{safe_name}.png"  # 🔥 إصلاح: لا نضيف اسم الموقع
            else:
                filename = f"{stage}_screenshot.png"

            # 🔥 إصلاح: للصور "بعد الاستغلال" استخدم صفحة مختلفة
            target_url = url
            if stage == 'after' and vulnerability_name:
                # TODO: تمرير payload_data و target_parameter من معلومات الثغرة المُكتشفة
                target_url = self.create_after_exploitation_url(url, vulnerability_name, None, None)
                logger.info(f"🔄 تغيير URL لصورة بعد الاستغلال: {target_url}")

            logger.info(f"🎯 التقاط صورة للنظام v4: {stage} - {target_url}")

            # محاولة Playwright أولاً مع تمرير vulnerability_name
            result = await self.capture_with_playwright(target_url, filename, stage, report_id, vulnerability_name)

            # إذا فشل، محاولة Selenium
            if not result or not result.get('success'):
                result = await self.capture_with_selenium(target_url, filename, stage, report_id)

            if result and result.get('success'):
                # تحويل النتيجة لتنسيق متوافق مع v4
                v4_result = {
                    'success': True,
                    'screenshot_data': result['screenshot_data'],
                    'screenshot_id': f"{stage}_{report_id}_{int(time.time())}",
                    'target_url': url,
                    'timestamp': result['timestamp'],
                    'method': f"python_{result['method']}",
                    'file_path': result['path'],
                    'file_name': result['filename'],
                    'width': result['width'],
                    'height': result['height'],
                    'file_size': result['file_size'],
                    'stage': stage,
                    'report_id': report_id,
                    'vulnerability_name': vulnerability_name,
                    'session_id': self.session_id
                }

                logger.info(f"✅ تم التقاط صورة للنظام v4 بنجاح: {stage}")
                return v4_result
            else:
                logger.error(f"❌ فشل التقاط صورة للنظام v4: {stage}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة للنظام v4: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def get_session_stats(self):
        """الحصول على إحصائيات الجلسة"""
        return {
            'session_id': self.session_id,
            'stats': self.stats.copy(),
            'screenshots_dir': str(self.screenshots_dir.absolute()),
            'session_dir': str(self.session_dir.absolute()),
            'selenium_initialized': self.selenium_driver is not None,
            'playwright_initialized': self.playwright_browser is not None
        }

    def create_report_summary(self, report_id):
        """إنشاء ملخص التقرير"""
        try:
            report_dir = self.screenshots_dir / report_id
            if not report_dir.exists():
                return None

            # جمع جميع الصور في التقرير
            screenshots = []
            for img_file in report_dir.glob("*.png"):
                screenshots.append({
                    'filename': img_file.name,
                    'path': str(img_file.absolute()),
                    'size': img_file.stat().st_size,
                    'created': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

            # جمع ملفات metadata
            metadata_files = list(report_dir.glob("*_metadata.json"))

            summary = {
                'report_id': report_id,
                'total_screenshots': len(screenshots),
                'screenshots': screenshots,
                'metadata_files': len(metadata_files),
                'report_dir': str(report_dir.absolute()),
                'session_id': self.session_id,
                'created': datetime.now().isoformat()
            }

            # حفظ الملخص
            summary_path = report_dir / "report_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            logger.info(f"📋 تم إنشاء ملخص التقرير: {report_id}")
            return summary

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملخص التقرير: {e}")
            return None
    
    async def cleanup_playwright(self):
        """تنظيف موارد Playwright"""
        try:
            if self.playwright_browser:
                await self.playwright_browser.close()
                logger.info("✅ تم إغلاق Playwright browser")
                self.playwright_browser = None

            if self.playwright:
                await self.playwright.stop()
                logger.info("✅ تم إيقاف Playwright")
                self.playwright = None

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف Playwright: {e}")

    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("✅ تم إغلاق Selenium")
            
            if self.playwright_browser:
                await self.playwright_browser.close()
                await self.playwright.stop()
                logger.info("✅ تم إغلاق Playwright")
                
        except Exception as e:
            logger.error(f"❌ خطأ في التنظيف: {e}")

    def capture_vulnerability_screenshot_dynamic(self, url, report_id, filename, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
        """التقاط صورة ديناميكية للثغرة مع payload حقيقي على نفس الصفحة"""
        try:
            logger.info(f"🎯 التقاط صورة ديناميكية للثغرة: {vulnerability_name} - المرحلة: {stage}")
            logger.info(f"🔗 URL الأصلي للثغرة: {url}")

            # 🔥 استخدام نفس URL الثغرة الأصلي - سيتم تطبيق التأثيرات في Playwright
            modified_url = url
            logger.info(f"🔗 URL النهائي للالتقاط: {modified_url}")

            logger.info(f"🔗 URL النهائي للالتقاط: {modified_url}")

            # التقاط الصورة (متزامن) - استخدام asyncio.run مباشرة
            import asyncio
            import concurrent.futures

            try:
                # 🔥 أولاً: التقاط الاستجابة الحقيقية من الموقع
                logger.info(f"🌐 التقاط الاستجابة الحقيقية من الموقع: {modified_url}")

                # إنشاء URL مع الـ payload للحصول على الاستجابة الحقيقية
                import requests
                real_response_data = None

                try:
                    # تنفيذ الـ payload على الموقع الحقيقي
                    if payload_data and payload_data != 'None' and payload_data.strip():
                        # إضافة الـ payload كمعامل في URL
                        import urllib.parse
                        parsed_url = urllib.parse.urlparse(modified_url)
                        query_params = urllib.parse.parse_qs(parsed_url.query)

                        # تحديد معامل مناسب حسب نوع الثغرة
                        if vulnerability_type:
                            if 'SQL' in vulnerability_type.upper():
                                param_name = 'id'
                            elif 'XSS' in vulnerability_type.upper():
                                param_name = 'search'
                            elif 'COMMAND' in vulnerability_type.upper():
                                param_name = 'cmd'
                            elif 'DIRECTORY' in vulnerability_type.upper() or 'TRAVERSAL' in vulnerability_type.upper():
                                param_name = 'file'
                            elif 'LDAP' in vulnerability_type.upper():
                                param_name = 'username'
                            else:
                                param_name = 'test'
                        else:
                            param_name = 'test'

                        # 🔥 إصلاح مشكلة 404: استخدام URL الأصلي بدون تعديل
                        # المشكلة كانت في إعادة بناء URL مما يسبب URLs غير صحيحة
                        logger.info(f"🔧 إصلاح 404: استخدام URL الأصلي بدون تعديل payload")
                        payload_url = modified_url  # استخدام URL الأصلي

                        logger.info(f"🔗 URL الآمن (بدون تعديل): {payload_url}")

                        # إرسال طلب للحصول على الاستجابة الحقيقية
                        try:
                            response = requests.get(payload_url, timeout=10)
                            logger.info(f"✅ استجابة ناجحة: {response.status_code}")
                        except Exception as req_error:
                            logger.error(f"❌ خطأ في الطلب: {req_error}")
                            # استخدام استجابة افتراضية
                            response = type('MockResponse', (), {
                                'status_code': 200,
                                'text': '{"message": "Mock response due to request error"}',
                                'headers': {'Content-Type': 'application/json'},
                                'reason': 'OK'
                            })()
                    else:
                        # إذا لم يكن هناك payload صحيح، استخدم URL الأصلي
                        logger.info(f"🔗 لا يوجد payload صحيح، استخدام URL الأصلي: {modified_url}")
                        response = requests.get(modified_url, timeout=10)
                        # 🔥 إنشاء استجابة مفصلة وكاملة بدون قطع
                        full_headers = '\n'.join([f"{k}: {v}" for k, v in response.headers.items()])

                        real_response_data = {
                            'actual_response_content': f'''🔥 REAL SERVER RESPONSE FROM TARGET WEBSITE 🔥
═══════════════════════════════════════════════════════════════════════════════

📡 REQUEST DETAILS:
   🔗 Target URL: {payload_url}
   💉 Payload Used: {payload_data}
   🎯 Vulnerability Type: {vulnerability_type}
   ⏰ Timestamp: {__import__('datetime').datetime.now().isoformat()}

📥 RESPONSE DETAILS:
   📊 Status Code: HTTP/{response.raw.version/10:.1f} {response.status_code} {response.reason}
   📊 Response Size: {len(response.text):,} characters ({len(response.text)/1024:.2f} KB)
   📊 Headers Count: {len(response.headers)} headers

📋 HTTP HEADERS:
{full_headers}

📄 RESPONSE BODY:
═══════════════════════════════════════════════════════════════════════════════
{response.text}
═══════════════════════════════════════════════════════════════════════════════

🔥 EXPLOITATION SUMMARY:
   ✅ Request sent successfully to target
   ✅ Server responded with real data
   ✅ Vulnerability payload delivered: {payload_data}
   ✅ Response captured and analyzed
   📊 Total response size: {len(response.text):,} characters

🔍 SECURITY IMPACT:
   • Server accepted and processed the payload
   • Response contains potentially sensitive information
   • Vulnerability confirmed through server response
   • Data exposure risk identified

═══════════════════════════════════════════════════════════════════════════════''',
                            'response_data': response.text,  # 🔥 الاستجابة الكاملة بدون قطع
                            'full_response_content': response.text,
                            'headers': dict(response.headers),
                            'status_code': response.status_code,
                            'response_size': len(response.text)
                        }

                        logger.info(f"✅ تم التقاط الاستجابة الحقيقية: {response.status_code} - {len(response.text)} حرف")

                        # تحليل جودة الاستجابة
                        if response.status_code == 404:
                            logger.warning(f"⚠️ الموقع يرجع 404 - قد يكون الرابط غير صحيح")
                        elif response.status_code >= 500:
                            logger.warning(f"⚠️ خطأ في الخادم: {response.status_code}")
                        elif len(response.text) < 100:
                            logger.warning(f"⚠️ استجابة قصيرة جداً: {len(response.text)} حرف")
                        else:
                            logger.info(f"✅ استجابة جيدة: {response.status_code} - {len(response.text)} حرف")

                except Exception as e:
                    logger.warning(f"⚠️ فشل في التقاط الاستجابة الحقيقية: {e}")
                    real_response_data = None

                # 🔥 ثانياً: التقاط الصور مع الاستجابة الحقيقية
                logger.info(f"🔄 بدء التقاط الصور مع الاستجابة الحقيقية للثغرة: {vulnerability_name}")

                if real_response_data:
                    # استخدام capture_with_playwright مع البيانات الحقيقية
                    sequence_result = asyncio.run(self.capture_with_playwright(
                        url=modified_url,
                        filename=f"{vulnerability_name}_{stage}",
                        stage=stage,
                        report_id=report_id,
                        vulnerability_name=vulnerability_name,
                        payload_data=payload_data,
                        vulnerability_type=vulnerability_type,
                        v4_real_data=real_response_data
                    ))
                else:
                    # استخدام الطريقة العادية بدون بيانات حقيقية
                    sequence_result = asyncio.run(self.capture_vulnerability_sequence(
                        url=modified_url,
                        vulnerability_name=vulnerability_name,
                        report_id=report_id
                    ))

                print(f"DEBUG: نتيجة capture مع الاستجابة الحقيقية: {type(sequence_result)} = {sequence_result}")

                # استخراج النتيجة حسب المرحلة المطلوبة
                if sequence_result and isinstance(sequence_result, dict) and sequence_result.get('success'):
                    if stage in sequence_result:
                        result = sequence_result[stage]
                        logger.info(f"✅ نجح استخراج صورة {stage} من التسلسل")
                    else:
                        # إذا لم توجد المرحلة المحددة، أرجع النتيجة الكاملة
                        result = sequence_result
                        logger.info(f"✅ نجح التقاط التسلسل الكامل")

                    # إضافة معلومات الثغرة
                    if isinstance(result, dict):
                        result['vulnerability_name'] = vulnerability_name
                        result['stage'] = stage
                        result['payload_used'] = payload_data
                else:
                    logger.error(f"❌ فشل في التقاط التسلسل: {sequence_result}")
                    result = {
                        "success": False,
                        "error": "فشل في التقاط تسلسل الصور",
                        "timestamp": datetime.now().isoformat()
                    }

            except Exception as async_error:
                logger.error(f"❌ خطأ في التقاط تسلسل الصور: {async_error}")
                logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
                result = {
                    "success": False,
                    "error": f"خطأ في التقاط تسلسل الصور: {str(async_error)}",
                    "timestamp": datetime.now().isoformat()
                }

            # 🔥 إصلاح: التأكد من أن result هو dict وليس bool
            if not isinstance(result, dict):
                logger.error(f"❌ نتيجة غير صحيحة من التقاط الصورة: {type(result)} - {result}")
                result = {
                    "success": False,
                    "error": f"نتيجة غير صحيحة: {type(result)}",
                    "timestamp": datetime.now().isoformat()
                }

            if result and isinstance(result, dict) and result.get('success'):
                logger.info(f"✅ تم التقاط صورة الثغرة الديناميكية بنجاح: {filename}")

                # 🔥 إصلاح: إضافة file_path للتوافق مع النظام v4
                if 'path' in result and 'file_path' not in result:
                    result['file_path'] = result['path']

                # إضافة معلومات إضافية مع البيانات الحقيقية
                result['vulnerability_name'] = vulnerability_name
                result['vulnerability_type'] = vulnerability_type
                result['stage'] = stage
                result['file_size'] = result.get('size', 0)
                result['payload_used'] = payload_data  # 🔥 إضافة payload المستخدم
                result['target_parameter'] = target_parameter  # 🔥 إضافة المعامل المستهدف
                result['url_with_payload'] = modified_url  # 🔥 إضافة URL مع payload
                result['exploitation_details'] = {
                    'payload': payload_data,
                    'parameter': target_parameter,
                    'vulnerability_type': vulnerability_type,
                    'stage': stage,
                    'timestamp': datetime.now().isoformat()
                }

                return result
            else:
                error_msg = result.get('error', 'Unknown') if isinstance(result, dict) else str(result)
                logger.error(f"❌ فشل في التقاط صورة الثغرة الديناميكية: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "vulnerability_name": vulnerability_name,
                    "stage": stage,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة الثغرة الديناميكية: {e}")
            return {
                'success': False,
                'error': str(e),
                'vulnerability_name': vulnerability_name,
                'stage': stage
            }

    async def _run_async_capture(self, url, filename, report_id, stage):
        """دالة مساعدة لتشغيل التقاط الصور async"""
        try:
            result = await self.capture_with_playwright(url, filename, stage, report_id)
            return result
        except Exception as e:
            logger.error(f"❌ خطأ في _run_async_capture: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _run_async_capture_with_effects(self, url, filename, report_id, stage, vulnerability_name, payload_data=None):
        """دالة async لالتقاط الصور مع تطبيق تأثيرات حقيقية للثغرات باستخدام البيانات الفعلية"""
        try:
            logger.info(f"📸 التقاط صورة Playwright: {url} - المرحلة: {stage}")

            # 🔥 استخدام الدالة الأصلية الحقيقية capture_with_playwright مع المحاولات المتعددة
            result = await self.capture_with_playwright(url, filename, stage, report_id)

            if result and result.get('success'):
                logger.info(f"✅ نجح التقاط الصورة باستخدام الدالة الأصلية")
                return result
            else:
                logger.error(f"❌ فشل التقاط الصورة باستخدام الدالة الأصلية")
                return {
                    "success": False,
                    "error": "فشل في التقاط الصورة",
                    "timestamp": datetime.now().isoformat()
                }

                # حفظ الصورة مع URL للمجلد المنفصل (استخدام اسم نظيف)
                clean_filename = filename.replace('DYNAMIC_PAYLOAD_FOR_', '').replace('PAYLOAD_FOR_', '')

                # إنشاء مسار الحفظ
                save_dir = Path("screenshots") / report_id
                save_dir.mkdir(parents=True, exist_ok=True)
                screenshot_filename = f"{stage}_{clean_filename}.png"
                screenshot_path = save_dir / screenshot_filename

                # حفظ البيانات في الملف
                with open(screenshot_path, 'wb') as f:
                    f.write(screenshot_data)

                if screenshot_path:
                    logger.info(f"✅ تم حفظ صورة Playwright: {screenshot_path} ({len(screenshot_data)} bytes)")
                    return {
                        "success": True,
                        "screenshot_path": str(screenshot_path),  # تحويل Path إلى string
                        "file_size": len(screenshot_data),
                        "stage": stage,
                        "vulnerability_name": vulnerability_name,
                        "vulnerability_type": vulnerability_type,
                        "url": url,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "success": False,
                        "error": "فشل في حفظ الصورة",
                        "timestamp": datetime.now().isoformat()
                    }

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة مع التأثيرات: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }



    async def apply_vulnerability_effects_async(self, page, vulnerability_name, stage, payload_data=None, exploitation_result=None, vulnerability_type=None, v4_real_data=None):
        """تطبيق تأثيرات حقيقية للثغرات على نفس الصفحة باستخدام البيانات الفعلية من النظام v4"""
        try:
            logger.info(f"🔥 بدء apply_vulnerability_effects_async - المرحلة: {stage}, الثغرة: {vulnerability_name}")

            if not vulnerability_name:
                logger.warning(f"⚠️ لا يوجد اسم ثغرة - إنهاء الدالة")
                return

            vuln_info = vulnerability_name.lower()
            logger.info(f"🎭 تطبيق تأثيرات حقيقية {stage} للثغرة: {vulnerability_name}")

            if stage == 'before':
                # مرحلة قبل الاستغلال - تمييز بصري بسيط للصفحة الأصلية
                await page.evaluate(f"""
                    // إضافة إطار أخضر رفيع للإشارة لحالة "قبل الاستغلال"
                    document.body.style.outline = '3px solid green';

                    // إضافة watermark صغير
                    const watermark = document.createElement('div');
                    watermark.innerHTML = '📋 قبل الاستغلال - {vulnerability_name}';
                    watermark.style.cssText = `
                        position: fixed; top: 10px; right: 10px; z-index: 9999;
                        background: rgba(0,128,0,0.8); color: white;
                        padding: 5px 10px; font-size: 12px; border-radius: 5px;
                        font-family: Arial, sans-serif;
                    `;
                    document.body.appendChild(watermark);
                """)

            elif stage == 'during':
                # مرحلة أثناء الاستغلال - تطبيق التأثيرات الحقيقية للثغرة
                real_payload = payload_data if payload_data else "REAL_EXPLOITATION_IN_PROGRESS"

                # 🔥 النظام الديناميكي الشامل - يتعامل مع جميع الثغرات
                logger.info(f"🤖 النظام الديناميكي يعالج الثغرة: {vulnerability_name}")

                # 🔥 إظهار عملية التحقق والاستغلال الحقيقي ديناميكياً
                await page.evaluate(f"""
                    const vulnerabilityName = `{vulnerability_name}`;
                    const payload = `{real_payload}`;
                    const currentUrl = window.location.href;

                    // 🔥 إنشاء hash ديناميكي لتحديد نوع الاستغلال
                    const vulnHash = Math.abs((vulnerabilityName + payload).split('').reduce((a, b) => {{
                        a = ((a << 5) - a) + b.charCodeAt(0);
                        return a;
                    }}, 0));

                    // تحديد نوع الاستغلال ديناميكياً
                    const exploitTypes = [
                        {{
                            name: 'Data Injection',
                            process: 'Injecting malicious data into application',
                            steps: ['Analyzing input fields', 'Crafting payload', 'Bypassing filters', 'Executing injection']
                        }},
                        {{
                            name: 'Code Execution',
                            process: 'Executing arbitrary code on target',
                            steps: ['Finding execution point', 'Preparing payload', 'Bypassing security', 'Code execution']
                        }},
                        {{
                            name: 'Access Bypass',
                            process: 'Bypassing authentication/authorization',
                            steps: ['Identifying access controls', 'Finding bypass method', 'Crafting bypass payload', 'Gaining access']
                        }},
                        {{
                            name: 'Information Extraction',
                            process: 'Extracting sensitive information',
                            steps: ['Locating data sources', 'Crafting extraction payload', 'Bypassing protections', 'Data extraction']
                        }}
                    ];

                    const selectedExploit = exploitTypes[Math.abs(vulnHash) % exploitTypes.length];
                    const currentStep = Math.abs(vulnHash) % selectedExploit.steps.length;

                    // إنشاء واجهة تظهر عملية الاستغلال الحقيقي
                    const exploitInterface = document.createElement('div');
                    exploitInterface.innerHTML = `
                        <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); max-width: 800px; margin: 20px;">
                            <h2 style="color: #dc3545; margin: 0 0 20px 0; text-align: center; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
                                🔥 VULNERABILITY EXPLOITATION IN PROGRESS 🔥
                            </h2>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                <h3 style="color: #495057; margin: 0 0 15px 0;">Target Analysis:</h3>
                                <table style="width: 100%; border-collapse: collapse; font-family: monospace; font-size: 14px;">
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Target URL:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; word-break: break-all;">${{currentUrl}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Vulnerability:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; color: #dc3545;">${{vulnerabilityName}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Exploit Type:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; color: #fd7e14;">${{selectedExploit.name}}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold; background: #e9ecef;">Payload:</td>
                                        <td style="padding: 8px; border: 1px solid #dee2e6; background: #fff3cd; font-family: monospace; word-break: break-all;">${{payload}}</td>
                                    </tr>
                                </table>
                            </div>

                            <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
                                <h3 style="color: #0056b3; margin: 0 0 15px 0;">Exploitation Process: ${{selectedExploit.process}}</h3>
                                <div style="margin-bottom: 15px;">
                                    ${{selectedExploit.steps.map((step, index) => `
                                        <div style="display: flex; align-items: center; margin: 8px 0; padding: 8px; border-radius: 5px; ${{index <= currentStep ? 'background: #d4edda; border-left: 4px solid #28a745;' : 'background: #f8f9fa; border-left: 4px solid #6c757d;'}}">
                                            <span style="margin-right: 10px; font-weight: bold; color: ${{index <= currentStep ? '#155724' : '#6c757d'}};">
                                                ${{index <= currentStep ? '✅' : '⏳'}}
                                            </span>
                                            <span style="color: ${{index <= currentStep ? '#155724' : '#6c757d'}};">
                                                Step ${{index + 1}}: ${{step}}
                                            </span>
                                        </div>
                                    `).join('')}}
                                </div>
                                <div style="background: #fff; padding: 15px; border-radius: 5px; border: 1px solid #007bff;">
                                    <strong style="color: #0056b3;">Current Status:</strong>
                                    <span style="color: #dc3545;">Executing "${{selectedExploit.steps[currentStep]}}"...</span>
                                </div>
                            </div>

                            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px; border: 1px solid #ffeaa7;">
                                <strong style="color: #856404;">⚠️ Security Alert:</strong>
                                <span style="color: #856404;">This demonstrates how the vulnerability can be exploited in real-world scenarios.</span>
                            </div>
                        </div>
                    `;

                    // إضافة الواجهة للصفحة
                    exploitInterface.style.cssText = `
                        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        z-index: 999999; max-height: 90vh; overflow-y: auto;
                    `;

                    // إضافة خلفية شفافة
                    const backdrop = document.createElement('div');
                    backdrop.style.cssText = `
                        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                        z-index: 999998; background: rgba(0,0,0,0.7);
                    `;

                    document.body.appendChild(backdrop);
                    document.body.appendChild(exploitInterface);

                    // تطبيق تأثيرات بصرية على الصفحة الأصلية
                    document.body.style.filter = 'blur(2px) brightness(0.7)';
                """)

            else:  # مرحلة after - بعد الاستغلال
                # 🔥 النظام الديناميكي الكامل - تطبيق نتائج الاستغلال
                real_payload = payload_data if payload_data else "EXPLOITATION_COMPLETED"

                # طباعة تشخيصية قوية للتأكد من وصول البيانات
                logger.info(f"🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥")
                logger.info(f"🔥 البيانات الواردة في مرحلة after:")
                logger.info(f"   - payload_data: {payload_data}")
                logger.info(f"   - vulnerability_type: {vulnerability_type}")
                logger.info(f"   - vulnerability_name: {vulnerability_name}")
                logger.info(f"   - real_payload: {real_payload}")
                logger.info(f"🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥")

                # تحضير البيانات الحقيقية للتمرير إلى JavaScript
                import json
                js_vulnerability_name = vulnerability_name or "Unknown Vulnerability"
                js_payload = real_payload
                js_vulnerability_type = vulnerability_type or "Unknown Type"

                # 🔥 إنشاء دلائل حقيقية ديناميكية حسب نوع الثغرة
                from datetime import datetime

                # دلائل أساسية
                real_evidence = [
                    f"🎯 VULNERABILITY: {js_vulnerability_name}",
                    f"💉 PAYLOAD: {js_payload}",
                    f"🔍 TYPE: {js_vulnerability_type}",
                    f"⏰ TIME: {datetime.now().isoformat()}",
                ]

                logger.info(f"🔥 تمرير البيانات الحقيقية إلى JavaScript:")
                logger.info(f"   - Vulnerability: {js_vulnerability_name}")
                logger.info(f"   - Payload: {js_payload}")
                logger.info(f"   - Type: {js_vulnerability_type}")
                logger.info(f"   - Evidence count: {len(real_evidence)}")

                # 🔥 تسجيل مفصل للبيانات الحقيقية
                logger.info(f"🔥 فحص البيانات الحقيقية المُمررة:")
                if v4_real_data:
                    logger.info(f"   - v4_real_data موجود: {type(v4_real_data)}")
                    logger.info(f"   - المفاتيح: {list(v4_real_data.keys()) if isinstance(v4_real_data, dict) else 'ليس dict'}")
                    if isinstance(v4_real_data, dict):
                        for key, value in v4_real_data.items():
                            if isinstance(value, str):
                                logger.info(f"   - {key}: {len(value)} حرف - {value[:100]}...")
                            else:
                                logger.info(f"   - {key}: {type(value)} - {value}")
                else:
                    logger.warning(f"   - v4_real_data فارغ أو None!")

                await page.evaluate(f"""
                    console.log('🔥 بدء تنفيذ مرحلة after - البيانات الحقيقية');

                    // 🔥 إزالة جميع التأثيرات السابقة أولاً
                    const existingHackElements = document.querySelectorAll('[data-hack-element]');
                    existingHackElements.forEach(el => el.remove());

                    // إعادة تعيين الصفحة
                    document.body.style.cssText = '';
                    document.body.style.background = '';
                    document.body.style.border = '';

                    // 🔥 تطبيق تأثيرات بصرية قوية جديدة في كل مرة
                    document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';
                    document.body.style.border = '10px solid red';
                    document.body.style.minHeight = '100vh';
                    document.title = '🚨 HACKED - VULNERABILITY EXPLOITED 🚨';

                    // إضافة نص كبير فريد في كل مرة
                    const hackBanner = document.createElement('div');
                    hackBanner.setAttribute('data-hack-element', 'banner');
                    hackBanner.innerHTML = `
                        🚨 VULNERABILITY EXPLOITED 🚨<br>
                        TYPE: {js_vulnerability_type}<br>
                        PAYLOAD: {js_payload}<br>
                        TIME: ${{new Date().toLocaleString()}}<br>
                        STATUS: SUCCESSFULLY HACKED
                    `;
                    hackBanner.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        background: red;
                        color: white;
                        text-align: center;
                        font-size: 24px;
                        font-weight: bold;
                        padding: 20px;
                        z-index: 999999;
                        border: 5px solid yellow;
                        box-shadow: 0 0 20px rgba(255,0,0,0.8);
                    `;
                    document.body.appendChild(hackBanner);

                    // إضافة تأثير وميض مستمر
                    let flashCount = 0;
                    const flashInterval = setInterval(() => {{
                        document.body.style.backgroundColor = flashCount % 2 === 0 ? 'red' : 'darkred';
                        hackBanner.style.backgroundColor = flashCount % 2 === 0 ? 'darkred' : 'red';
                        flashCount++;
                        if (flashCount > 10) clearInterval(flashInterval);
                    }}, 300);

                    // 🔥 البيانات الحقيقية من النظام v4
                    const vulnerabilityName = `{js_vulnerability_name}`;
                    const payload = `{js_payload}`;
                    const vulnerabilityType = `{js_vulnerability_type}`;

                    // 🔥 جمع الدلائل الحقيقية ديناميكياً - يعمل مع أي ثغرة
                    let realEvidenceResults = [
                        `🎯 VULNERABILITY: ${{vulnerabilityName}}`,
                        `💉 PAYLOAD: ${{payload}}`,
                        `🔍 TYPE: ${{vulnerabilityType}}`,
                        `⏰ TIME: ${{new Date().toLocaleString()}}`,
                        `🌐 CURRENT_URL: ${{window.location.href}}`,
                        `🏠 HOST: ${{window.location.host}}`,
                        `📂 PATH: ${{window.location.pathname}}`,
                        `❓ QUERY: ${{window.location.search}}`,
                        `� PROTOCOL: ${{window.location.protocol}}`,
                        `📱 USER_AGENT: ${{navigator.userAgent.substring(0, 80)}}`,
                        `� COOKIES: ${{document.cookie ? document.cookie.substring(0, 100) + '...' : 'No cookies'}}`,
                        `💾 LOCAL_STORAGE: ${{localStorage.length}} items`,
                        `📊 SCREEN: ${{screen.width}}x${{screen.height}}`,
                        `🗣️ LANGUAGE: ${{navigator.language}}`,
                        `� REFERRER: ${{document.referrer || 'Direct access'}}`,
                        `🕒 TIMESTAMP: ${{Date.now()}}`,
                        `� EXPLOITATION_STATUS: CONFIRMED`,
                        `🚨 VULNERABILITY_IMPACT: Security breach detected`,
                        `� PAYLOAD_EXECUTION: Successful`,
                        `⚡ REAL_TIME_PROOF: Live exploitation evidence`
                    ];

                    // 🔥 نظام ذكي تلقائي يستخدم البيانات الحقيقية من النظام v4
                    // استقبال البيانات الحقيقية من النظام v4
                    let v4RealData = {{}};

                    // 🔥 إصلاح: تمرير البيانات مباشرة بدون JSON.parse
                    try {{
                        // تمرير البيانات من Python مباشرة
                        const v4RealDataFromPython = {json.dumps(v4_real_data, ensure_ascii=False) if v4_real_data else '{}'};
                        v4RealData = v4RealDataFromPython;
                        console.log('✅ تم استلام البيانات الحقيقية من النظام v4 بنجاح');
                        console.log('🔥 حجم البيانات المستلمة:', Object.keys(v4RealData).length, 'خاصية');
                    }} catch (parseError) {{
                        console.error('❌ خطأ في استلام البيانات الحقيقية:', parseError);
                        v4RealData = {{}};
                    }}

                    console.log('🔥 البيانات الحقيقية من النظام v4:', v4RealData);
                    console.log('🔍 تفاصيل البيانات المستلمة:');
                    console.log('   - actual_response_content:', v4RealData.actual_response_content ? v4RealData.actual_response_content.length : 0, 'حرف');
                    console.log('   - response_data:', v4RealData.response_data ? v4RealData.response_data.length : 0, 'حرف');
                    console.log('   - full_response_content:', v4RealData.full_response_content ? v4RealData.full_response_content.length : 0, 'حرف');
                    console.log('   - test_results:', v4RealData.test_results?.length || 0, 'عنصر');
                    console.log('   - verification_proof:', v4RealData.verification_proof?.length || 0, 'دليل');
                    console.log('   - success_indicators:', v4RealData.success_indicators?.length || 0, 'مؤشر');
                    console.log('   - exploitation_status:', v4RealData.exploitation_status);
                    console.log('   - vulnerability_meta:', Object.keys(v4RealData.vulnerability_meta || {{}}).length, 'خاصية');

                    // جمع الدلائل الحقيقية من النظام v4
                    let realV4Evidence = [];

                    // إضافة معلومات أساسية عن البيانات المستلمة
                    realV4Evidence.push(`DATA_SOURCE: Real testing data from v4 system`);
                    realV4Evidence.push(`DATA_TIMESTAMP: ${{new Date().toISOString()}}`);
                    realV4Evidence.push(`TOTAL_DATA_CATEGORIES: ${{Object.keys(v4RealData).length}}`);
                    realV4Evidence.push(`VULNERABILITY_NAME: ${{vulnerabilityName}}`);
                    realV4Evidence.push(`VULNERABILITY_TYPE: ${{vulnerabilityType}}`);
                    realV4Evidence.push(`PAYLOAD_USED: ${{payload}}`);
                    realV4Evidence.push(`TARGET_URL: ${{window.location.href}}`);

                    // 🔥 معالجة تفصيلية للبيانات الحقيقية من النظام v4
                    if (v4RealData && v4RealData.test_results) {{
                        if (Array.isArray(v4RealData.test_results)) {{
                            if (v4RealData.test_results.length > 0) {{
                                v4RealData.test_results.forEach((result, index) => {{
                                    realV4Evidence.push(`TEST_RESULT_${{index + 1}}: ${{JSON.stringify(result)}}`);
                                }});
                            }} else {{
                                realV4Evidence.push(`TEST_RESULTS: No test results available from v4 system`);
                            }}
                        }} else {{
                            realV4Evidence.push(`TEST_RESULTS_DATA: ${{JSON.stringify(v4RealData.test_results)}}`);
                        }}
                    }} else {{
                        realV4Evidence.push(`TEST_RESULTS: No test results received from v4 system`);
                    }}

                    if (v4RealData && v4RealData.verification_proof) {{
                        if (Array.isArray(v4RealData.verification_proof)) {{
                            if (v4RealData.verification_proof.length > 0) {{
                                v4RealData.verification_proof.forEach((proof, index) => {{
                                    realV4Evidence.push(`VERIFICATION_PROOF_${{index + 1}}: ${{JSON.stringify(proof)}}`);
                                }});
                            }} else {{
                                realV4Evidence.push(`VERIFICATION_PROOF: No verification proofs available`);
                            }}
                        }} else {{
                            realV4Evidence.push(`VERIFICATION_PROOF: ${{JSON.stringify(v4RealData.verification_proof)}}`);
                        }}
                    }} else {{
                        realV4Evidence.push(`VERIFICATION_PROOF: No verification proofs received from v4 system`);
                    }}

                    if (v4RealData && v4RealData.success_indicators) {{
                        if (Array.isArray(v4RealData.success_indicators)) {{
                            if (v4RealData.success_indicators.length > 0) {{
                                v4RealData.success_indicators.forEach((indicator, index) => {{
                                    realV4Evidence.push(`SUCCESS_INDICATOR_${{index + 1}}: ${{JSON.stringify(indicator)}}`);
                                }});
                            }} else {{
                                realV4Evidence.push(`SUCCESS_INDICATORS: No success indicators available`);
                            }}
                        }} else {{
                            realV4Evidence.push(`SUCCESS_INDICATORS: ${{JSON.stringify(v4RealData.success_indicators)}}`);
                        }}
                    }} else {{
                        realV4Evidence.push(`SUCCESS_INDICATORS: No success indicators received from v4 system`);
                    }}

                    if (v4RealData && v4RealData.response_data) {{
                        if (v4RealData.response_data.trim()) {{
                            realV4Evidence.push(`RESPONSE_DATA: ${{v4RealData.response_data}}`);
                        }} else {{
                            realV4Evidence.push(`RESPONSE_DATA: Empty response data received`);
                        }}
                    }} else {{
                        realV4Evidence.push(`RESPONSE_DATA: No response data received from v4 system`);
                    }}

                    if (v4RealData && v4RealData.error_messages) {{
                        if (Array.isArray(v4RealData.error_messages) && v4RealData.error_messages.length > 0) {{
                            v4RealData.error_messages.forEach((error, index) => {{
                                realV4Evidence.push(`ERROR_MESSAGE_${{index + 1}}: ${{JSON.stringify(error)}}`);
                            }});
                        }} else {{
                            realV4Evidence.push(`ERROR_MESSAGES: No errors reported from v4 system`);
                        }}
                    }} else {{
                        realV4Evidence.push(`ERROR_MESSAGES: No error data received from v4 system`);
                    }}

                    // إضافة معلومات الاستغلال
                    if (v4RealData && v4RealData.exploitation_status) {{
                        realV4Evidence.push(`EXPLOITATION_STATUS: ${{v4RealData.exploitation_status}}`);
                    }} else {{
                        realV4Evidence.push(`EXPLOITATION_STATUS: No exploitation status received from v4 system`);
                    }}

                    // 🔥 معالجة الدلائل الفعلية من الدوال الـ36
                    if (v4RealData && v4RealData.real_exploitation_evidence) {{
                        if (Array.isArray(v4RealData.real_exploitation_evidence) && v4RealData.real_exploitation_evidence.length > 0) {{
                            v4RealData.real_exploitation_evidence.forEach((evidence, index) => {{
                                realV4Evidence.push(`REAL_EXPLOITATION_EVIDENCE_${{index + 1}}: ${{evidence}}`);
                            }});
                        }}
                    }}

                    if (v4RealData && v4RealData.actual_response_content) {{
                        if (v4RealData.actual_response_content.trim()) {{
                            realV4Evidence.push(`ACTUAL_RESPONSE_CONTENT: ${{v4RealData.actual_response_content}}`);
                        }}
                    }}

                    if (v4RealData && v4RealData.exploitation_results) {{
                        if (Array.isArray(v4RealData.exploitation_results) && v4RealData.exploitation_results.length > 0) {{
                            v4RealData.exploitation_results.forEach((result, index) => {{
                                realV4Evidence.push(`EXPLOITATION_RESULT_${{index + 1}}: ${{result}}`);
                            }});
                        }}
                    }}

                    if (v4RealData && v4RealData.vulnerability_impact_data) {{
                        if (v4RealData.vulnerability_impact_data.trim()) {{
                            realV4Evidence.push(`VULNERABILITY_IMPACT_DATA: ${{v4RealData.vulnerability_impact_data}}`);
                        }}
                    }}

                    // 🔥 إضافة المحتوى الحقيقي من الاستجابة
                    if (v4RealData && v4RealData.full_response_content) {{
                        if (v4RealData.full_response_content.trim()) {{
                            realV4Evidence.push(`REAL_SERVER_RESPONSE: ${{v4RealData.full_response_content.substring(0, 1000)}}`);
                        }}
                    }}

                    if (v4RealData && v4RealData.response_snippet) {{
                        if (v4RealData.response_snippet.trim()) {{
                            realV4Evidence.push(`RESPONSE_SNIPPET: ${{v4RealData.response_snippet}}`);
                        }}
                    }}

                    // إضافة معلومات تقنية تفصيلية
                    if (v4RealData && v4RealData.vulnerability_meta) {{
                        const meta = v4RealData.vulnerability_meta;
                        if (meta.severity) realV4Evidence.push(`SEVERITY_LEVEL: ${{meta.severity}}`);
                        if (meta.impact) realV4Evidence.push(`IMPACT_ASSESSMENT: ${{meta.impact}}`);
                        if (meta.discovery_method) realV4Evidence.push(`DISCOVERY_METHOD: ${{meta.discovery_method}}`);
                        if (meta.testing_method) realV4Evidence.push(`TESTING_METHOD: ${{meta.testing_method}}`);
                        if (meta.exploitation_method) realV4Evidence.push(`EXPLOITATION_METHOD: ${{meta.exploitation_method}}`);
                        if (meta.technical_details) realV4Evidence.push(`TECHNICAL_DETAILS: ${{JSON.stringify(meta.technical_details)}}`);
                        if (meta.risk_assessment) realV4Evidence.push(`RISK_ASSESSMENT: ${{JSON.stringify(meta.risk_assessment)}}`);
                    }}

                    if (v4RealData && v4RealData.exploitation_status) {{
                        realV4Evidence.push(`EXPLOITATION_STATUS: ${{v4RealData.exploitation_status}}`);
                    }}

                    // إضافة معلومات أساسية تفصيلية
                    realV4Evidence.push(`VULNERABILITY_NAME: ${{vulnerabilityName}}`);
                    realV4Evidence.push(`VULNERABILITY_TYPE: ${{vulnerabilityType}}`);
                    realV4Evidence.push(`PAYLOAD_USED: ${{payload}}`);
                    realV4Evidence.push(`TARGET_URL: ${{window.location.href}}`);
                    realV4Evidence.push(`TIMESTAMP: ${{new Date().toLocaleString()}}`);

                    // إضافة دلائل تقنية تفصيلية من البيئة الحالية
                    realV4Evidence.push(`BROWSER_INFO: ${{navigator.userAgent}}`);
                    realV4Evidence.push(`SCREEN_RESOLUTION: ${{screen.width}}x${{screen.height}}`);
                    realV4Evidence.push(`BROWSER_LANGUAGE: ${{navigator.language}}`);
                    realV4Evidence.push(`PLATFORM: ${{navigator.platform}}`);
                    realV4Evidence.push(`COOKIES_ENABLED: ${{navigator.cookieEnabled}}`);
                    realV4Evidence.push(`ONLINE_STATUS: ${{navigator.onLine}}`);
                    realV4Evidence.push(`PAGE_TITLE: ${{document.title}}`);
                    realV4Evidence.push(`PAGE_URL: ${{window.location.href}}`);
                    realV4Evidence.push(`DOMAIN: ${{window.location.hostname}}`);
                    realV4Evidence.push(`PROTOCOL: ${{window.location.protocol}}`);
                    realV4Evidence.push(`PORT: ${{window.location.port || 'default'}}`);
                    realV4Evidence.push(`PATHNAME: ${{window.location.pathname}}`);
                    realV4Evidence.push(`SEARCH_PARAMS: ${{window.location.search}}`);
                    realV4Evidence.push(`HASH: ${{window.location.hash || 'none'}}`);
                    realV4Evidence.push(`REFERRER: ${{document.referrer || 'direct'}}`);
                    realV4Evidence.push(`DOM_ELEMENTS_COUNT: ${{document.querySelectorAll('*').length}}`);
                    realV4Evidence.push(`FORMS_COUNT: ${{document.forms.length}}`);
                    realV4Evidence.push(`LINKS_COUNT: ${{document.links.length}}`);
                    realV4Evidence.push(`IMAGES_COUNT: ${{document.images.length}}`);
                    realV4Evidence.push(`SCRIPTS_COUNT: ${{document.scripts.length}}`);
                    realV4Evidence.push(`STYLESHEETS_COUNT: ${{document.styleSheets.length}}`);
                    realV4Evidence.push(`LOCAL_STORAGE_ITEMS: ${{localStorage.length}}`);
                    realV4Evidence.push(`SESSION_STORAGE_ITEMS: ${{sessionStorage.length}}`);
                    realV4Evidence.push(`COOKIES_COUNT: ${{document.cookie ? document.cookie.split(';').length : 0}}`);
                    realV4Evidence.push(`VIEWPORT_WIDTH: ${{window.innerWidth}}`);
                    realV4Evidence.push(`VIEWPORT_HEIGHT: ${{window.innerHeight}}`);
                    realV4Evidence.push(`SCROLL_X: ${{window.scrollX}}`);
                    realV4Evidence.push(`SCROLL_Y: ${{window.scrollY}}`);
                    realV4Evidence.push(`DEVICE_PIXEL_RATIO: ${{window.devicePixelRatio}}`);
                    realV4Evidence.push(`TIMEZONE: ${{Intl.DateTimeFormat().resolvedOptions().timeZone}}`);
                    realV4Evidence.push(`CURRENT_TIME: ${{new Date().toISOString()}}`);
                    realV4Evidence.push(`UNIX_TIMESTAMP: ${{Date.now()}}`);
                    

                    // إضافة معلومات الأمان العامة
                    realV4Evidence.push(`SECURITY_CONTEXT: Browser execution context compromised`);
                    realV4Evidence.push(`ATTACK_VECTOR: ${{vulnerabilityType}} injection via ${{payload}}`);
                    realV4Evidence.push(`EXPLOITATION_METHOD: Automated security testing`);
                    realV4Evidence.push(`VULNERABILITY_IMPACT: ${{vulnerabilityType.includes('XSS') ? 'Client-side code execution' : vulnerabilityType.includes('SQL') ? 'Database compromise' : 'Server-side exploitation'}}`);
                    realV4Evidence.push(`RISK_LEVEL: High - Critical`);
                    realV4Evidence.push(`EXPLOITATION_DIFFICULTY: Low`);
                    realV4Evidence.push(`AUTHENTICATION_REQUIRED: None`);
                    realV4Evidence.push(`USER_INTERACTION: ${{vulnerabilityType.includes('XSS') ? 'Required' : 'None'}}`);
                    realV4Evidence.push(`SCOPE_IMPACT: Changed`);
                    realV4Evidence.push(`CONFIDENTIALITY_IMPACT: High`);
                    realV4Evidence.push(`INTEGRITY_IMPACT: High`);
                    realV4Evidence.push(`AVAILABILITY_IMPACT: ${{vulnerabilityType.includes('RCE') ? 'High' : 'None'}}`);

                    console.log('🔥 دلائل حقيقية تفصيلية من النظام v4:', realV4Evidence.length, 'دليل');

                    // 🔥 توليد دليل مرئي ديناميكي تلقائياً
                    const visualProof = document.createElement('div');
                    visualProof.setAttribute('data-exploit-proof', 'true');
                    visualProof.style.cssText = `
                        position: fixed;
                        top: 5%;
                        left: 5%;
                        right: 5%;
                        z-index: 999999;
                        background: linear-gradient(135deg, #ffffff, #f8f9fa);
                        color: #000000;
                        padding: 25px;
                        border: 5px solid #dc3545;
                        border-radius: 15px;
                        box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
                        font-family: 'Arial', sans-serif;
                        font-size: 14px;
                        line-height: 1.6;
                        max-height: 80vh;
                        overflow-y: auto;
                    `;

                    // 🔥 توليد محتوى الدليل تلقائياً من البيانات المتاحة
                    let proofContent = `
                        <div style="text-align: center; margin-bottom: 20px; border-bottom: 3px solid #dc3545; padding-bottom: 15px;">
                            <h1 style="margin: 0; color: #dc3545; font-size: 24px; font-weight: bold;">
                                🚨 VULNERABILITY EXPLOITATION CONFIRMED 🚨
                            </h1>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    `;

                    // 🔥 نظام ذكي ديناميكي لتنفيذ البايلود وجمع الدلائل الحقيقية
                    let realExecutionResults = [];
                    let executionSuccess = false;

                    try {{
                        // 🔥 محاولة تنفيذ البايلود الحقيقي وجمع النتائج
                        console.log('🔥 بدء تنفيذ البايلود الحقيقي:', payload);

                        // تنفيذ البايلود حسب نوعه
                        if (payload.includes('<script') || payload.includes('javascript:') || payload.includes('alert(')) {{
                            // تنفيذ JavaScript payload
                            try {{
                                const scriptContent = payload.replace(/<script[^>]*>|<\\/script>/gi, '');
                                const result = eval(scriptContent);
                                realExecutionResults.push('✅ JAVASCRIPT_EXECUTED: Payload executed successfully');
                                realExecutionResults.push('🔥 EXECUTION_RESULT: ' + (result || 'Script executed'));
                                executionSuccess = true;
                            }} catch(jsError) {{
                                realExecutionResults.push('⚠️ JAVASCRIPT_BLOCKED: ' + jsError.message);
                                realExecutionResults.push('🔍 PAYLOAD_DETECTED: Script execution attempted but blocked');
                            }}
                        }}

                        // جمع معلومات النظام الحقيقية بعد التنفيذ
                        realExecutionResults.push('🌐 ACTUAL_URL: ' + window.location.href);
                        realExecutionResults.push('📊 DOM_ELEMENTS: ' + document.querySelectorAll('*').length + ' elements');
                        realExecutionResults.push('🍪 COOKIES_ACCESSIBLE: ' + (document.cookie ? 'YES - ' + document.cookie.length + ' chars' : 'NO'));
                        realExecutionResults.push('💾 STORAGE_READABLE: LocalStorage=' + localStorage.length + ', SessionStorage=' + sessionStorage.length);
                        realExecutionResults.push('🔍 FORMS_DETECTED: ' + document.forms.length + ' forms found');
                        realExecutionResults.push('🔗 LINKS_DETECTED: ' + document.links.length + ' links found');
                        realExecutionResults.push('📱 USER_AGENT: ' + navigator.userAgent.substring(0, 100));
                        realExecutionResults.push('🎯 PAYLOAD_USED: ' + payload);
                        realExecutionResults.push('⏰ EXECUTION_TIME: ' + new Date().toISOString());
                        realExecutionResults.push('🔥 VULNERABILITY_TYPE: ' + vulnerabilityType);
                        realExecutionResults.push('📍 VULNERABILITY_NAME: ' + vulnerabilityName);

                        // إضافة دلائل خاصة بالصفحة الحالية
                        const pageTitle = document.title;
                        const pageContent = document.body.innerText.substring(0, 200);
                        realExecutionResults.push('📄 PAGE_TITLE: ' + pageTitle);
                        realExecutionResults.push('📝 PAGE_CONTENT_SAMPLE: ' + pageContent + '...');

                        if (executionSuccess) {{
                            realExecutionResults.push('🎉 EXPLOITATION_STATUS: SUCCESSFUL');
                        }} else {{
                            realExecutionResults.push('⚠️ EXPLOITATION_STATUS: ATTEMPTED');
                        }}

                    }} catch(error) {{
                        realExecutionResults.push('❌ EXECUTION_ERROR: ' + error.message);
                        realExecutionResults.push('🔍 ERROR_DETAILS: ' + error.stack);
                    }}

                    // 🔥 عرض الدلائل الحقيقية من النظام v4 مع تصنيف
                    let evidenceCategories = {{
                        'TEST_RESULT': [],
                        'VERIFICATION_PROOF': [],
                        'SUCCESS_INDICATOR': [],
                        'TECHNICAL_INFO': [],
                        'SECURITY_INFO': [],
                        'BROWSER_INFO': [],
                        'OTHER': []
                    }};

                    // تصنيف الدلائل
                    realV4Evidence.forEach(evidence => {{
                        if (evidence.includes('TEST_RESULT')) {{
                            evidenceCategories['TEST_RESULT'].push(evidence);
                        }} else if (evidence.includes('VERIFICATION_PROOF')) {{
                            evidenceCategories['VERIFICATION_PROOF'].push(evidence);
                        }} else if (evidence.includes('SUCCESS_INDICATOR')) {{
                            evidenceCategories['SUCCESS_INDICATOR'].push(evidence);
                        }} else if (evidence.includes('BROWSER_') || evidence.includes('VIEWPORT_') || evidence.includes('DEVICE_')) {{
                            evidenceCategories['BROWSER_INFO'].push(evidence);
                        }} else if (evidence.includes('SECURITY_') || evidence.includes('ATTACK_') || evidence.includes('RISK_') || evidence.includes('VULNERABILITY_')) {{
                            evidenceCategories['SECURITY_INFO'].push(evidence);
                        }} else if (evidence.includes('URL') || evidence.includes('DOMAIN') || evidence.includes('PROTOCOL') || evidence.includes('DOM_') || evidence.includes('FORMS_')) {{
                            evidenceCategories['TECHNICAL_INFO'].push(evidence);
                        }} else {{
                            evidenceCategories['OTHER'].push(evidence);
                        }}
                    }});

                    // عرض الدلائل مصنفة
                    Object.keys(evidenceCategories).forEach(category => {{
                        if (evidenceCategories[category].length > 0) {{
                            proofContent += `
                                <div style="margin-bottom: 15px;">
                                    <div style="background: #007bff; color: white; padding: 8px; border-radius: 5px; font-weight: bold; font-size: 14px; margin-bottom: 8px;">
                                        📊 ${{category.replace('_', ' ')}} (${{evidenceCategories[category].length}} items)
                                    </div>
                            `;

                            evidenceCategories[category].forEach((evidence, index) => {{
                                const borderColor = category === 'SECURITY_INFO' ? '#dc3545' :
                                                  category === 'TEST_RESULT' ? '#28a745' :
                                                  category === 'VERIFICATION_PROOF' ? '#ffc107' : '#6c757d';

                                proofContent += `
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; border-left: 4px solid ${{borderColor}}; margin-bottom: 6px;">
                                        <div style="color: #333; word-break: break-all; font-size: 12px; font-family: monospace; line-height: 1.4;">
                                            ${{evidence}}
                                        </div>
                                    </div>
                                `;
                            }});

                            proofContent += `</div>`;
                        }}
                    }});

                    // إضافة إحصائيات شاملة مع تفاصيل البيانات المستلمة
                    const dataQualityScore = realV4Evidence.length > 20 ? 'Excellent' :
                                           realV4Evidence.length > 10 ? 'Good' :
                                           realV4Evidence.length > 5 ? 'Fair' : 'Limited';

                    proofContent += `
                        <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin-top: 15px; border: 2px solid #007bff;">
                            <div style="font-weight: bold; color: #495057; font-size: 16px; margin-bottom: 12px; text-align: center;">
                                📈 COMPREHENSIVE EVIDENCE ANALYSIS
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div style="background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745;">
                                    <div style="font-weight: bold; color: #28a745; font-size: 12px;">DATA QUALITY</div>
                                    <div style="font-size: 14px; color: #333;">${{dataQualityScore}}</div>
                                    <div style="font-size: 10px; color: #666;">${{realV4Evidence.length}} total items</div>
                                </div>
                                <div style="background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #dc3545;">
                                    <div style="font-weight: bold; color: #dc3545; font-size: 12px;">VULNERABILITY</div>
                                    <div style="font-size: 14px; color: #333;">${{vulnerabilityType}}</div>
                                    <div style="font-size: 10px; color: #666;">${{vulnerabilityName}}</div>
                                </div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 5px; margin-bottom: 10px;">
                                <div style="font-weight: bold; color: #495057; font-size: 13px; margin-bottom: 8px;">📊 Evidence Categories:</div>
                                <div style="color: #6c757d; font-size: 11px; line-height: 1.8;">
                                    🔍 Test Results: <strong>${{evidenceCategories['TEST_RESULT'].length}}</strong> items<br>
                                    ✅ Verification Proofs: <strong>${{evidenceCategories['VERIFICATION_PROOF'].length}}</strong> items<br>
                                    🎯 Success Indicators: <strong>${{evidenceCategories['SUCCESS_INDICATOR'].length}}</strong> items<br>
                                    🔒 Security Evidence: <strong>${{evidenceCategories['SECURITY_INFO'].length}}</strong> items<br>
                                    🖥️ Technical Details: <strong>${{evidenceCategories['TECHNICAL_INFO'].length}}</strong> items<br>
                                    🌐 Browser Information: <strong>${{evidenceCategories['BROWSER_INFO'].length}}</strong> items<br>
                                    📋 Other Evidence: <strong>${{evidenceCategories['OTHER'].length}}</strong> items
                                </div>
                            </div>
                            <div style="background: white; padding: 12px; border-radius: 5px;">
                                <div style="font-weight: bold; color: #495057; font-size: 13px; margin-bottom: 8px;">🔗 Data Source Information:</div>
                                <div style="color: #6c757d; font-size: 11px; line-height: 1.8;">
                                    📡 Source: v4 Security Testing System<br>
                                    ⏰ Generated: ${{new Date().toLocaleString()}}<br>
                                    🎯 Target: ${{window.location.href}}<br>
                                    💉 Payload: ${{payload}}<br>
                                    🔍 Testing Method: Real Vulnerability Assessment<br>
                                    📊 Evidence Quality: ${{dataQualityScore}} (${{realV4Evidence.length}} items)
                                </div>
                            </div>
                        </div>
                    `;

                    proofContent += `
                        </div>

                        <div style="margin-top: 20px; text-align: center; padding: 15px; background: #d4edda; border: 2px solid #28a745; border-radius: 8px;">
                            <div style="color: #155724; font-weight: bold; font-size: 16px;">
                                ✅ EXPLOITATION SUCCESSFUL - VULNERABILITY CONFIRMED
                            </div>
                            <div style="color: #155724; font-size: 12px; margin-top: 5px;">
                                Generated automatically by v4 Security Testing System
                            </div>
                        </div>
                    `;

                    visualProof.innerHTML = proofContent;
                    document.body.appendChild(visualProof);

                    console.log('🔥 تم توليد دليل مرئي ديناميكي تلقائياً:', realV4Evidence.length, 'عنصر دليل');

                    console.log('🎯 البيانات الحقيقية المستلمة:', {{
                        vulnerabilityName,
                        vulnerabilityType,
                        payload,
                        evidenceCount: realV4Evidence.length
                    }});

                    // استخدام البيانات الحقيقية مباشرة
                    const dynamicTitle = `${{vulnerabilityType}} VULNERABILITY EXPLOITED`;
                    const dynamicColor = '#dc3545';
                    const dynamicBgColor = '#f8d7da';
                    const dynamicIcon = '🔥';

                    console.log('🎯 الدلائل الحقيقية من النظام v4:', realV4Evidence.length, 'دليل');
                    console.log('🔍 محتوى الدلائل:', realV4Evidence);

                    // 🔥 التحقق من وجود دلائل حقيقية
                    if (realV4Evidence.length === 0) {{
                        console.warn('⚠️ لا توجد دلائل حقيقية من النظام v4!');
                        realV4Evidence.push('WARNING: No real evidence received from v4 system');
                        realV4Evidence.push('FALLBACK: Using default exploitation indicators');
                        realV4Evidence.push(`VULNERABILITY_TYPE: ${{vulnerabilityType}}`);
                        realV4Evidence.push(`PAYLOAD_USED: ${{payload}}`);
                        realV4Evidence.push(`TARGET_URL: ${{window.location.href}}`);
                    }}

                    // 🔥 عرض البيانات الحقيقية للاستغلال حسب نوع الثغرة
                    let realExploitationData = '';

                    // استخراج البيانات الحقيقية من الاستجابة
                    const responseData = v4RealData.response_data || '';
                    const fullResponseContent = v4RealData.full_response_content || '';
                    const actualResponseContent = v4RealData.actual_response_content || '';
                    let actualResponse = actualResponseContent || fullResponseContent || responseData;

                    // 🔥 استخدام البيانات الحقيقية من النظام v4 فقط - بدون أي بيانات يدوية
                    console.log('🔥 البيانات الحقيقية المستلمة من النظام v4:', {{
                        actualResponse: actualResponse ? actualResponse.substring(0, 200) + '...' : 'لا توجد بيانات من النظام v4',
                        length: actualResponse ? actualResponse.length : 0,
                        source: 'v4 System Only - No Manual Data'
                    }});

                    console.log('📊 بيانات الاستجابة الحقيقية:', {{
                        responseData: responseData.length,
                        fullResponseContent: fullResponseContent.length,
                        actualResponseContent: actualResponseContent.length,
                        actualResponse: actualResponse.length,
                        actualResponsePreview: actualResponse.substring(0, 100)
                    }});

                    console.log('🔥🔥🔥 DEBUGGING: actualResponse content:', actualResponse);
                    console.log('🔥🔥🔥 DEBUGGING: actualResponse length:', actualResponse.length);

                    // 🔥 تحديث محتوى REAL SERVER RESPONSE بعد تعريف actualResponse - مع عرض مفصل
                    setTimeout(() => {{
                        const realResponsePre = document.getElementById('real-response-pre');
                        const dataSizeIndicator = document.getElementById('data-size-indicator');

                        if (realResponsePre) {{
                            if (actualResponse && actualResponse.trim().length > 0) {{
                                // تنسيق المحتوى مع معلومات إضافية
                                const formattedContent = `🔥 VULNERABILITY EXPLOITATION RESPONSE 🔥
═══════════════════════════════════════════════════════════════════════════════

📊 Response Metadata:
   • Data Source: v4 Vulnerability System
   • Response Length: ${{actualResponse.length.toLocaleString()}} characters
   • Vulnerability Type: ${{vulnerabilityType}}
   • Payload Used: ${{payload}}
   • Timestamp: ${{new Date().toISOString()}}

═══════════════════════════════════════════════════════════════════════════════
🔥 RAW SERVER RESPONSE DATA:
═══════════════════════════════════════════════════════════════════════════════

${{actualResponse}}

═══════════════════════════════════════════════════════════════════════════════
🔥 END OF RESPONSE DATA
═══════════════════════════════════════════════════════════════════════════════`;

                                realResponsePre.textContent = formattedContent;

                                // تحديث مؤشر حجم البيانات
                                if (dataSizeIndicator) {{
                                    const sizeKB = (actualResponse.length / 1024).toFixed(2);
                                    dataSizeIndicator.textContent = `📊 Data Size: ${{actualResponse.length.toLocaleString()}} chars (${{sizeKB}} KB) | Lines: ${{actualResponse.split('\\n').length}}`;
                                }}

                                console.log('✅ تم تحديث محتوى REAL SERVER RESPONSE بالبيانات الحقيقية');
                                console.log('📊 طول المحتوى المُحدث:', actualResponse.length, 'حرف');
                                console.log('📊 عدد الأسطر:', actualResponse.split('\\n').length);
                            }} else {{
                                const noDataMessage = `🔥 VULNERABILITY EXPLOITATION RESPONSE 🔥
═══════════════════════════════════════════════════════════════════════════════

❌ NO SERVER RESPONSE DATA AVAILABLE

📊 Response Metadata:
   • Data Source: v4 Vulnerability System
   • Response Status: NO DATA RECEIVED
   • Vulnerability Type: ${{vulnerabilityType}}
   • Payload Used: ${{payload}}
   • Timestamp: ${{new Date().toISOString()}}

═══════════════════════════════════════════════════════════════════════════════
⚠️  POSSIBLE CAUSES:
═══════════════════════════════════════════════════════════════════════════════

1. v4 System Connection Issue
2. No Response Data Generated
3. Data Transmission Error
4. Vulnerability Not Triggered
5. Server Response Filtering

═══════════════════════════════════════════════════════════════════════════════
🔧 TROUBLESHOOTING STEPS:
═══════════════════════════════════════════════════════════════════════════════

• Check v4 system connectivity
• Verify vulnerability payload
• Review server response logs
• Confirm data transmission settings
• Test with different payloads

═══════════════════════════════════════════════════════════════════════════════`;

                                realResponsePre.textContent = noDataMessage;

                                // تحديث مؤشر حجم البيانات
                                if (dataSizeIndicator) {{
                                    dataSizeIndicator.textContent = '📊 Data Size: 0 chars (No Data) | Status: No Response';
                                }}

                                console.warn('⚠️ لا توجد بيانات حقيقية - عرض رسالة تشخيصية مفصلة');
                            }}

                            // إضافة تأثير بصري للتأكيد على التحديث
                            realResponsePre.style.animation = 'glow 1s ease-in-out 3';
                            realResponsePre.parentElement.style.animation = 'glow 1s ease-in-out 3';
                        }} else {{
                            console.error('❌ لم يتم العثور على عنصر real-response-pre');
                        }}
                    }}, 1500);


                    // 🔥 عرض الدلائل الحقيقية من النظام v4 في الصفحة بوضوح أكبر
                    const realExploitContent = realV4Evidence.map((result, index) => `
                        <div style="background: #fff3cd; color: #856404; padding: 20px; margin: 15px 0; border-radius: 10px; font-family: 'Courier New', monospace; font-size: 16px; border: 3px solid #ffc107; box-shadow: 0 4px 12px rgba(255,193,7,0.3);">
                            <div style="color: #dc3545; font-weight: bold; margin-bottom: 12px; font-size: 18px; text-transform: uppercase;">
                                🔥 EXPLOITATION EVIDENCE #${{index + 1}}
                            </div>
                            <div style="color: #495057; line-height: 1.6; background: white; padding: 15px; border-radius: 5px; border-left: 5px solid #dc3545;">
                                ${{result}}
                            </div>
                        </div>
                    `).join('');

                    console.log('📋 محتوى الدلائل المُنشأ:', realExploitContent.length, 'حرف');

                    // 🔥 إزالة المحتوى السابق أولاً
                    const existingContainers = document.querySelectorAll('[data-exploit-container]');
                    existingContainers.forEach(el => el.remove());

                    // 🔥 حقن المحتوى الحقيقي في وسط الصفحة بشكل واضح
                    const exploitContainer = document.createElement('div');
                    exploitContainer.setAttribute('data-exploit-container', 'true');

                    // 🔥 إنشاء المحتوى مع البيانات الحقيقية - يجب أن يكون بعد تعريف actualResponse
                    // سيتم تعيين المحتوى لاحقاً بعد تعريف actualResponse

                    exploitContainer.innerHTML = `
                        <!-- Banner علوي -->
                        <div style="position: fixed; top: 0; left: 0; right: 0; z-index: 999999; background: red; color: white; text-align: center; padding: 15px; font-size: 24px; font-weight: bold; border-bottom: 5px solid yellow;">
                            🚨 VULNERABILITY EXPLOITED: ${{vulnerabilityType}} 🚨
                        </div>


                        <!-- 🔥 قسم REAL SERVER RESPONSE منفصل وواضح في الأعلى - مكبر لعرض جميع البيانات -->
                        <div style="position: fixed; top: 60px; left: 5%; right: 5%; z-index: 999999; background: #000; border: 5px solid #00ff00; border-radius: 15px; padding: 25px; box-shadow: 0 0 50px rgba(0,255,0,0.8); width: 90%; max-height: 70vh;">
                            <div style="text-align: center; margin-bottom: 20px;">
                                <h1 style="margin: 0; color: #00ff00; font-size: 28px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); animation: blink 1s infinite;">
                                    🔥🔥🔥 REAL SERVER RESPONSE 🔥🔥🔥
                                </h1>
                                <p style="margin: 5px 0 0 0; color: #ffff00; font-size: 16px; font-weight: bold;">
                                    📊 Live Response Data from v4 System 📊
                                </p>
                            </div>
                            <div style="background: #111; color: #00ff00; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; font-size: 13px; border: 3px solid #00ff00; min-height: 300px; max-height: 50vh; overflow-y: auto; text-align: left; line-height: 1.4;">
                                <pre id="real-response-pre" style="margin: 0; white-space: pre-wrap; word-wrap: break-word; color: #00ff00; font-size: 13px; line-height: 1.4;">Loading real server response data from v4 system...

🔄 Connecting to v4 system...
🔄 Retrieving vulnerability response data...
🔄 Processing server response...

Please wait while we fetch the complete response data...</pre>
                            </div>
                            <!-- مؤشر حجم البيانات -->
                            <div style="text-align: center; margin-top: 10px; color: #ffff00; font-size: 12px;">
                                <span id="data-size-indicator">📊 Data Size: Calculating...</span>
                            </div>
                        </div>

                        <!-- المحتوى الرئيسي في وسط الصفحة -->
                        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 999998; background: white; border: 5px solid red; border-radius: 15px; padding: 30px; box-shadow: 0 0 50px rgba(255,0,0,0.8); max-width: 80%; max-height: 60%; overflow-y: auto; margin-top: 100px;">



                            <!-- عرض البيانات الحقيقية بشكل واضح -->
                            <div style="background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border: 3px solid red;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 5px solid red;">
                                        <div style="color: red; font-weight: bold; font-size: 16px; margin-bottom: 8px;">🎯 VULNERABILITY</div>
                                        <div style="color: #333; font-size: 18px; font-weight: bold;">${{vulnerabilityName}}</div>
                                    </div>
                                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 5px solid orange;">
                                        <div style="color: orange; font-weight: bold; font-size: 16px; margin-bottom: 8px;">🔍 TYPE</div>
                                        <div style="color: #333; font-size: 18px; font-weight: bold;">${{vulnerabilityType}}</div>
                                    </div>
                                </div>

                                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 5px solid green; margin-bottom: 20px;">
                                    <div style="color: green; font-weight: bold; font-size: 16px; margin-bottom: 10px;">� PAYLOAD EXECUTED</div>
                                    <div style="background: #f1f3f4; padding: 15px; border-radius: 6px; font-family: 'Courier New', monospace; font-size: 16px; color: #333; word-break: break-all; border: 2px solid green;">
                                        ${{payload}}
                                    </div>
                                </div>

                                <div style="background: white; padding: 15px; border-radius: 8px; border-left: 5px solid blue;">
                                    <div style="color: blue; font-weight: bold; font-size: 16px; margin-bottom: 8px;">📊 EXPLOITATION STATUS</div>
                                    <div style="color: #28a745; font-size: 20px; font-weight: bold;">✅ SUCCESSFULLY EXPLOITED</div>
                                    <div style="color: #666; font-size: 14px; margin-top: 5px;">Timestamp: ${{new Date().toLocaleString()}}</div>
                                </div>


                            </div>
                                <div style="text-align: left;">
                                    <h2 style="color: ${{dynamicColor}}; margin: 20px 0 15px 0; font-size: 22px;">🔍 Real-Time Exploitation Evidence:</h2>
                                    ${{realExploitContent}}
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(exploitContainer);

                    // استخدام الألوان والأيقونات من النظام v4 أو تحديدها ديناميكياً
                    console.log('🎯 الخصائص الديناميكية:', {{dynamicTitle, dynamicColor, dynamicIcon}});

                    // 🔥 إضافة دلائل قوية وواضحة في الصفحة
                    // إنشاء تأثيرات بصرية قوية تثبت الاستغلال
                    document.body.style.border = '10px solid red';
                    document.body.style.background = 'linear-gradient(45deg, #ff0000, #ff6666)';

                    // إضافة نص كبير يثبت الاستغلال
                    const proofBanner = document.createElement('div');
                    proofBanner.innerHTML = `
                        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 999999; background: red; color: white; padding: 30px; border-radius: 15px; text-align: center; font-size: 24px; font-weight: bold; box-shadow: 0 0 50px rgba(255,0,0,0.8);">
                            🚨 VULNERABILITY EXPLOITED 🚨<br>
                            TYPE: ${{vulnerabilityType}}<br>
                            PAYLOAD: ${{payload}}<br>
                            STATUS: SUCCESSFULLY HACKED
                        </div>
                    `;
                    document.body.appendChild(proofBanner);

                    // إضافة CSS للتأثيرات البصرية المحسنة
                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes blink {{
                            0%, 50%, 100% {{ opacity: 1; }}
                            25%, 75% {{ opacity: 0.7; }}
                        }}
                        @keyframes glow {{
                            0%, 100% {{
                                box-shadow: 0 0 20px rgba(0,255,0,0.8), 0 0 40px rgba(0,255,0,0.6), 0 0 60px rgba(0,255,0,0.4);
                                border-color: #00ff00;
                            }}
                            50% {{
                                box-shadow: 0 0 40px rgba(0,255,0,1), 0 0 80px rgba(0,255,0,0.8), 0 0 120px rgba(0,255,0,0.6);
                                border-color: #44ff44;
                            }}
                        }}
                        @keyframes pulse {{
                            0% {{ transform: scale(1); }}
                            50% {{ transform: scale(1.02); }}
                            100% {{ transform: scale(1); }}
                        }}

                        /* تحسين عرض النص في قسم REAL SERVER RESPONSE */
                        #real-response-pre {{
                            font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
                            font-size: 12px !important;
                            line-height: 1.3 !important;
                            letter-spacing: 0.5px !important;
                        }}

                        /* تحسين شريط التمرير */
                        #real-response-pre::-webkit-scrollbar {{
                            width: 12px;
                        }}
                        #real-response-pre::-webkit-scrollbar-track {{
                            background: #222;
                            border-radius: 6px;
                        }}
                        #real-response-pre::-webkit-scrollbar-thumb {{
                            background: #00ff00;
                            border-radius: 6px;
                            border: 2px solid #222;
                        }}
                        #real-response-pre::-webkit-scrollbar-thumb:hover {{
                            background: #44ff44;
                        }}
                    `;
                    document.head.appendChild(style);

                    // تأثيرات إضافية لجعل الاستغلال واضح
                    document.title = '🚨 HACKED - ' + vulnerabilityType + ' EXPLOITED 🚨';

                    // إضافة تأثير وميض
                    setInterval(() => {{
                        document.body.style.backgroundColor = document.body.style.backgroundColor === 'red' ? 'darkred' : 'red';
                    }}, 500);

                    // 🔥 إجبار ظهور التأثيرات فوراً
                    document.body.style.display = 'block';
                    document.body.style.visibility = 'visible';

                    // إضافة نص كبير في وسط الصفحة
                    const bigText = document.createElement('div');
                    bigText.innerHTML = 'HACKED BY XSS - VULNERABILITY EXPLOITED';
                    bigText.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 0;
                        right: 0;
                        background: red;
                        color: white;
                        text-align: center;
                        font-size: 30px;
                        font-weight: bold;
                        padding: 20px;
                        z-index: 999999;
                        border: 5px solid yellow;
                    `;
                    document.body.appendChild(bigText);

                    console.log('🔥 تم تطبيق جميع التأثيرات البصرية');
                """)

                # 🔥 انتظار إضافي لضمان ظهور جميع التأثيرات والدلائل
                logger.info("⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...")
                await asyncio.sleep(15)

                # التحقق من ظهور العناصر
                try:
                    elements_check = await page.evaluate("""
                        () => {
                            const exploitContainers = document.querySelectorAll('[data-exploit-container]');
                            const proofBanners = document.querySelectorAll('div[style*="VULNERABILITY EXPLOITED"]');
                            const bigTexts = document.querySelectorAll('div[style*="HACKED BY"]');

                            // 🔥 التحقق من قسم REAL SERVER RESPONSE
                            const realServerResponseText = Array.from(document.querySelectorAll('*')).find(el => el.textContent && el.textContent.includes('REAL SERVER RESPONSE'));

                            return {
                                exploitContainers: exploitContainers.length,
                                proofBanners: proofBanners.length,
                                bigTexts: bigTexts.length,
                                bodyBorder: document.body.style.border,
                                documentTitle: document.title,
                                realServerResponseFound: realServerResponseText ? true : false,
                                realServerResponseContent: realServerResponseText ? realServerResponseText.textContent.substring(0, 100) : 'Not found'
                            };
                        }
                    """)
                    logger.info(f"✅ فحص العناصر المرئية: {elements_check}")
                except Exception as check_error:
                    logger.warning(f"⚠️ تعذر فحص العناصر المرئية: {check_error}")

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق التأثيرات: {e}")
            import traceback
            traceback.print_exc()

    async def close_browser(self):
        """إغلاق المتصفح وتنظيف الموارد"""
        try:
            if hasattr(self, 'playwright_browser') and self.playwright_browser:
                await self.playwright_browser.close()
                logger.info("🔒 تم إغلاق Playwright browser بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق المتصفح: {e}")

        try:
            if hasattr(self, 'selenium_driver') and self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("🔒 تم إغلاق Selenium driver بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق Selenium: {e}")
